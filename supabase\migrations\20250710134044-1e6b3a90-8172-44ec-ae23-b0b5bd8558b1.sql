-- Update user_role enum to include the new roles
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'editor';
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'view';

-- Create a user_clients junction table for many-to-many relationship (editors can have multiple clients)
CREATE TABLE public.user_clients (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id, client_id)
);

-- Enable RLS
ALTER TABLE public.user_clients ENABLE ROW LEVEL SECURITY;

-- Create policies for user_clients
CREATE POLICY "Ad<PERSON> can manage all user-client relationships" 
ON public.user_clients 
FOR ALL 
TO authenticated
USING (get_user_role() = 'admin'::user_role);

-- Update the get_user_client_id function to work with both direct client_id and user_clients table
CREATE OR REPLACE FUNCTION public.get_user_client_id(user_uuid uuid DEFAULT auth.uid())
RETURNS uuid
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  -- First check if user has direct client_id (for 'view' users)
  SELECT COALESCE(
    (SELECT client_id FROM public.profiles WHERE user_id = user_uuid AND client_id IS NOT NULL),
    -- If no direct client_id, get the first one from user_clients table (for 'editor' users)
    (SELECT client_id FROM public.user_clients WHERE user_id = (SELECT id FROM public.profiles WHERE user_id = user_uuid) LIMIT 1)
  );
$$;

-- Create function to get all client IDs for a user (for editors with multiple clients)
CREATE OR REPLACE FUNCTION public.get_user_client_ids(user_uuid uuid DEFAULT auth.uid())
RETURNS uuid[]
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT CASE 
    WHEN (SELECT client_id FROM public.profiles WHERE user_id = user_uuid) IS NOT NULL THEN
      -- For users with direct client_id (view users)
      ARRAY[(SELECT client_id FROM public.profiles WHERE user_id = user_uuid)]
    ELSE
      -- For users in user_clients table (editors)
      ARRAY(SELECT client_id FROM public.user_clients WHERE user_id = (SELECT id FROM public.profiles WHERE user_id = user_uuid))
  END;
$$;