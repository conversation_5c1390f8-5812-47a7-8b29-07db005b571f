import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { TopBar } from '@/components/layout/TopBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { FileText, Brain, MessageSquare, Target, Download, Calendar as CalendarIcon, Code } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';
import { ReportUpload } from '@/components/admin/ReportUpload';
import ReportViewer from '@/components/reports/ReportViewer';
import { supabase } from '@/integrations/supabase/client';

const reportTypes = {
  'google-ai-overview': {
    title: 'Google AI Overview Report',
    icon: Brain,
    description: 'AI-powered insights from Google search results and trends'
  },
  'chatgpt-overview': {
    title: 'ChatGPT Overview Report', 
    icon: MessageSquare,
    description: 'ChatGPT analysis and conversation insights'
  },
  'brand-ai-overview': {
    title: 'Brand AI Overview',
    icon: Target,
    description: 'AI-driven brand analysis and competitive intelligence'
  }
};

// Helper function to get first available client for admin users
async function getFirstClientId(): Promise<string | null> {
  const { data } = await supabase
    .from('clients')
    .select('id')
    .limit(1)
    .maybeSingle();
  
  return data?.id || null;
}

export default function Reports() {
  const { reportType } = useParams();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [selectedCountry, setSelectedCountry] = useState('israel');
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [clients, setClients] = useState<Array<{id: string, name: string}>>([]);
  const [reportData, setReportData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [htmlTemplates, setHtmlTemplates] = useState<Array<{id: string, name: string, description: string}>>([]);
  const { profile } = useAuth();
  
  const isAdmin = profile?.role === 'admin';
  
  const currentReport = reportType ? reportTypes[reportType as keyof typeof reportTypes] : null;

  const countries = [
    { value: 'israel', label: 'Israel', flag: '🇮🇱' },
    { value: 'usa', label: 'United States', flag: '🇺🇸' },
    { value: 'uk', label: 'United Kingdom', flag: '🇬🇧' },
    { value: 'germany', label: 'Germany', flag: '🇩🇪' },
    { value: 'france', label: 'France', flag: '🇫🇷' },
  ];

  // Load clients for admin users
  const loadClients = async () => {
    if (!isAdmin) return;
    
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setClients(data || []);
      
      // Auto-select first client if none selected
      if (data && data.length > 0 && !selectedClient) {
        setSelectedClient(data[0].id);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  // Load HTML templates for current client
  const loadHtmlTemplates = async () => {
    try {
      const clientId = await getCurrentClientId();
      console.log('Loading HTML templates - clientId:', clientId, 'isAdmin:', isAdmin, 'selectedClient:', selectedClient);
      
      if (!clientId && !isAdmin) {
        console.log('No client ID and not admin - returning');
        return;
      }

      let query = supabase
        .from('html_templates')
        .select('id, name, description, client_id')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      // For admin users, if no client is selected, show all templates
      // For regular users or admin with selected client, filter by client_id
      if (clientId) {
        console.log('Filtering by client_id:', clientId);
        query = query.eq('client_id', clientId);
      } else {
        console.log('Not filtering by client_id - showing all templates');
      }

      const { data, error } = await query;
      console.log('Query result - data count:', data?.length || 0, 'error:', error);

      if (error) {
        console.error('Error loading HTML templates:', error);
        return;
      }
      
      if (data && data.length > 0) {
        console.log('Templates loaded:', data.map(t => ({ name: t.name, client_id: t.client_id, id: t.id })));
      } else {
        console.log('No templates found');
      }
      
      setHtmlTemplates(data || []);
    } catch (error) {
      console.error('Error loading HTML templates:', error);
    }
  };

  const getCurrentClientId = async () => {
    if (isAdmin) {
      return selectedClient || (await getFirstClientId());
    }
    return profile?.client_id;
  };

  const handleExportPDF = () => {
    console.log('Exporting PDF...');
  };

  const formatSelectedDate = () => {
    if (!selectedDate) return 'Select date';
    return format(selectedDate, 'dd/MM/yyyy');
  };

  const loadAvailableDates = async () => {
    try {
      const clientId = await getCurrentClientId();
      
      if (!clientId) {
        console.log('No client ID available');
        return;
      }

      const { data, error } = await supabase
        .from('report_data')
        .select('date_from')
        .eq('client_id', clientId)
        .not('date_from', 'is', null);

      if (error) {
        console.error('Error loading available dates:', error);
        return;
      }

      const dates = data?.map(item => item.date_from).filter(Boolean) || [];
      console.log('Available dates:', dates);
      setAvailableDates(dates);
    } catch (error) {
      console.error('Error loading available dates:', error);
    }
  };

  const loadReportData = async (reportType: string, date?: Date) => {
    if (!reportType) return;
    
    setIsLoading(true);
    try {
      // Check if this is an HTML template
      if (reportType.startsWith('html-template-')) {
        const templateId = reportType.replace('html-template-', '');
        
        const { data: templateData, error: templateError } = await supabase
          .from('html_templates')
          .select('*')
          .eq('id', templateId)
          .eq('is_active', true)
          .maybeSingle();

        if (templateError) {
          console.error('Error loading HTML template:', templateError);
          setReportData(null);
          setIsLoading(false);
          return;
        }

        if (!templateData) {
          console.log('No HTML template found');
          setReportData(null);
          setIsLoading(false);
          return;
        }

        // Now get the latest JSON data for this client
        const clientId = templateData.client_id;
        console.log('Loading latest JSON data for client:', clientId);

        // Get the most recent report data for this client matching the template type
        const reportTypeFromTemplate = templateData.name.toLowerCase().includes('chatgpt') ? 'chatgpt_results' : 
                                      templateData.name.toLowerCase().includes('google') ? 'google_ai_overview' : 
                                      'brand_ai_overview';
        
        let jsonQuery = supabase
          .from('report_data')
          .select('*')
          .eq('client_id', clientId)
          .eq('report_type', reportTypeFromTemplate)
          .order('created_at', { ascending: false });

        if (date) {
          const dateStr = format(date, 'yyyy-MM-dd');
          console.log('Filtering JSON data by date:', dateStr);
          jsonQuery = jsonQuery.eq('date_from', dateStr);
        }

        const { data: jsonData, error: jsonError } = await jsonQuery.limit(1).maybeSingle();

        if (jsonError && jsonError.code !== 'PGRST116') {
          console.error('Error loading JSON data for template:', jsonError);
        }

        console.log('Loaded template:', templateData);
        console.log('Loaded JSON data for template:', jsonData);

        setReportData({ 
          type: 'html_template', 
          template: templateData,
          data: jsonData?.data || {} 
        });
        setIsLoading(false);
        return;
      }

      // Handle regular report types
      let dbReportType = '';
      if (reportType === 'google-ai-overview') {
        dbReportType = 'google_ai_overview';
      } else if (reportType === 'chatgpt-overview') {
        dbReportType = 'chatgpt_results';
      } else if (reportType === 'brand-ai-overview') {
        dbReportType = 'brand_ai_overview';
      }

      const clientId = await getCurrentClientId();
      
      if (!clientId) {
        console.log('No client ID available for loading report data');
        setReportData(null);
        setIsLoading(false);
        return;
      }

      let query = supabase
        .from('report_data')
        .select('*')
        .eq('report_type', dbReportType)
        .eq('client_id', clientId)
        .order('created_at', { ascending: false });

      if (date) {
        const dateStr = format(date, 'yyyy-MM-dd');
        console.log('Filtering by date:', dateStr);
        query = query.eq('date_from', dateStr);
      } else {
        console.log('No date filter, loading most recent data');
      }

      console.log('Loading report data for:', { reportType: dbReportType, clientId, date });

      const { data, error } = await query.limit(1).maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading report data:', error);
        return;
      }

      console.log('Loaded report data:', data);
      setReportData(data);
    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load clients and dates when component mounts or dependencies change
  useEffect(() => {
    if (isAdmin) {
      loadClients();
    }
  }, [isAdmin]);

  useEffect(() => {
    if (profile?.client_id || (isAdmin && selectedClient)) {
      loadAvailableDates();
      loadHtmlTemplates();
    }
  }, [profile?.client_id, isAdmin, selectedClient]);

  useEffect(() => {
    if (reportType && (profile?.client_id || (isAdmin && selectedClient))) {
      loadReportData(reportType, selectedDate);
    }
  }, [reportType, selectedDate, profile?.client_id, isAdmin, selectedClient]);

  return (
    <SidebarProvider defaultOpen={false}>
      <div className="flex min-h-screen w-full">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          <main className="flex-1 p-6 space-y-6">
            <Breadcrumbs />
            
            {!currentReport ? (
              // Default reports overview
              <div className="space-y-6">
                <div className="flex items-center gap-2">
                  <FileText className="h-6 w-6" />
                  <h1 className="text-2xl font-semibold">Reports</h1>
                </div>
                
                {isAdmin && (
                  <div className="mb-6">
                    <ReportUpload onUploadComplete={() => {
                      console.log('Upload completed, refreshing data...');
                      loadAvailableDates();
                      loadHtmlTemplates();
                      if (reportType) {
                        loadReportData(reportType, selectedDate);
                      }
                    }} />
                  </div>
                )}
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries(reportTypes).map(([key, report]) => {
                    const Icon = report.icon;
                    return (
                      <Card key={key} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate(`/reports/${key}`)}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-muted rounded-lg">
                              <Icon className="h-5 w-5" />
                            </div>
                            <CardTitle className="text-lg">{report.title}</CardTitle>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground text-sm">{report.description}</p>
                        </CardContent>
                      </Card>
                    );
                  })}
                  
                  {/* HTML Templates */}
                  {htmlTemplates.map((template) => (
                    <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate(`/reports/html-template-${template.id}`)}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-muted rounded-lg">
                            <Code className="h-5 w-5" />
                          </div>
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground text-sm">{template.description || 'Custom HTML report template'}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              // Specific report view
              <div className="space-y-6">
                <div className="flex items-center gap-2">
                  <currentReport.icon className="h-6 w-6" />
                  <h1 className="text-2xl font-semibold">{currentReport.title}</h1>
                </div>
                
                {/* Filter Bar */}
                <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg border">
                  {/* PDF Export Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleExportPDF}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    PDF
                  </Button>

                  {/* Client Selector (Admin Only) */}
                  {isAdmin && (
                    <Select value={selectedClient} onValueChange={setSelectedClient}>
                      <SelectTrigger className="w-[200px]">
                        <SelectValue placeholder="Select client">
                          {clients.find(c => c.id === selectedClient)?.name || "Select client"}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {clients.map((client) => (
                          <SelectItem key={client.id} value={client.id}>
                            {client.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}

                  {/* Date Picker */}
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start text-left font-normal min-w-[200px]",
                          !selectedDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formatSelectedDate()}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={setSelectedDate}
                        className={cn("p-3 pointer-events-auto")}
                        modifiers={{
                          hasReport: (date) => {
                            const dateStr = format(date, 'yyyy-MM-dd');
                            return availableDates.includes(dateStr);
                          }
                        }}
                        modifiersStyles={{
                          hasReport: {
                            backgroundColor: 'hsl(var(--primary))',
                            color: 'hsl(var(--primary-foreground))',
                            fontWeight: 'bold',
                            borderRadius: '6px'
                          }
                        }}
                        modifiersClassNames={{
                          hasReport: 'bg-primary text-primary-foreground font-bold rounded-md'
                        }}
                      />
                    </PopoverContent>
                  </Popover>

                  {/* Country Selector */}
                  <Select value={selectedCountry} onValueChange={setSelectedCountry}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue>
                        <div className="flex items-center gap-2">
                          <span>{countries.find(c => c.value === selectedCountry)?.flag}</span>
                          <span>{countries.find(c => c.value === selectedCountry)?.label}</span>
                        </div>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country.value} value={country.value}>
                          <div className="flex items-center gap-2">
                            <span>{country.flag}</span>
                            <span>{country.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {isLoading ? (
                  <Card>
                    <CardContent className="p-6">
                      <div className="text-center py-12">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <h3 className="text-lg font-medium mb-2 text-foreground">Loading Report...</h3>
                        <p className="text-foreground/70">
                          Please wait while we load your report data.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ) : reportData ? (
                  reportData.type === 'html_template' ? (
                    <Card>
                      <CardContent className="p-0">
                        <div 
                          dangerouslySetInnerHTML={{ 
                            __html: (() => {
                              let htmlContent = reportData.template.html_content;
                              
                              // Inject the JSON data into the HTML template
                              if (reportData.data) {
                                const jsonDataStr = JSON.stringify(reportData.data, null, 2);
                                htmlContent = htmlContent.replace(/\{\{REPORT_DATA\}\}/g, jsonDataStr);
                                htmlContent = htmlContent.replace(/\{\{JSON_DATA\}\}/g, jsonDataStr);
                                
                                // Inject script to make data available globally
                                const scriptTag = `
                                  <script>
                                    window.reportData = ${jsonDataStr};
                                    window.jsonData = ${jsonDataStr};
                                    console.log('Injected latest report data:', window.reportData);
                                  </script>
                                `;
                                htmlContent = htmlContent.replace('</head>', `${scriptTag}</head>`);
                              }
                              
                              return htmlContent;
                            })()
                          }}
                          className="w-full min-h-[600px]"
                        />
                      </CardContent>
                    </Card>
                  ) : (
                    <ReportViewer reportData={reportData} />
                  )
                ) : (
                  <Card>
                    <CardContent className="p-6">
                      <div className="text-center py-12">
                        <currentReport.icon className="h-12 w-12 mx-auto mb-4 text-foreground/50" />
                        <h3 className="text-lg font-medium mb-2 text-foreground">No Reports Available</h3>
                        <p className="text-foreground/70">
                          {currentReport.description}. Upload a JSON file to view the report.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}