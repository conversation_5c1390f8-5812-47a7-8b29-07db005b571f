import { Clock, FileText, Upload, Eye, UserPlus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface Activity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  user?: string;
  icon: any;
  badge?: string;
}

export function RecentActivity() {
  const { data: activities, isLoading } = useQuery({
    queryKey: ['recent-activity'],
    queryFn: async () => {
      // Fetch recent reports
      const { data: reports } = await supabase
        .from('reports')
        .select(`
          id,
          title,
          created_at,
          type,
          profiles:uploaded_by(first_name, last_name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      // Fetch recent users
      const { data: users } = await supabase
        .from('profiles')
        .select('id, first_name, last_name, email, created_at')
        .order('created_at', { ascending: false })
        .limit(3);

      const activities: Activity[] = [];

      // Add report activities
      reports?.forEach(report => {
        const userName = report.profiles 
          ? `${report.profiles.first_name || ''} ${report.profiles.last_name || ''}`.trim() || report.profiles.email
          : 'Unknown user';
        
        activities.push({
          id: `report-${report.id}`,
          type: 'upload',
          description: `${userName} uploaded "${report.title}"`,
          timestamp: report.created_at,
          user: userName,
          icon: Upload,
          badge: report.type.replace('_', ' ').toUpperCase(),
        });
      });

      // Add user activities
      users?.forEach(user => {
        const userName = `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email;
        activities.push({
          id: `user-${user.id}`,
          type: 'user',
          description: `${userName} joined the platform`,
          timestamp: user.created_at,
          user: userName,
          icon: UserPlus,
        });
      });

      // Sort by timestamp
      return activities.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ).slice(0, 8);
    },
  });

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const past = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - past.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks}w ago`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-8 w-8 rounded-full bg-muted animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                  <div className="h-3 bg-muted rounded w-24 animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities?.map((activity) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="flex items-center space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                  <Icon className="h-4 w-4" />
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {activity.description}
                  </p>
                  <div className="flex items-center gap-2">
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(activity.timestamp)}
                    </p>
                    {activity.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {activity.badge}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
          
          {(!activities || activities.length === 0) && (
            <p className="text-sm text-muted-foreground text-center py-4">
              No recent activity to display
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}