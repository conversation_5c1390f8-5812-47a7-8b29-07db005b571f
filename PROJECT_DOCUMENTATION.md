# Black & White Dashboard - Project Documentation

## 📋 Project Overview

This is a **React + TypeScript** dashboard application built with modern web technologies. Originally created with Lovable.dev, this documentation will help you transition to VS Code for local development.

### 🚀 Tech Stack

- **Frontend Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 5.4.1
- **UI Framework**: shadcn/ui (Radix UI components)
- **Styling**: Tailwind CSS 3.4.11
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router DOM 6.26.2
- **Backend**: Supabase (Authentication & Database)
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts
- **Icons**: Lucide React
- **Package Manager**: npm (with bun.lockb present)

## 📁 Project Structure

```
black-white-dashboard-main/
├── public/                  # Static assets
├── src/                     # Source code
│   ├── components/          # Reusable UI components
│   ├── hooks/              # Custom React hooks
│   ├── integrations/       # External service integrations
│   ├── lib/                # Utility functions and configurations
│   ├── pages/              # Route components
│   │   ├── AddClient.tsx
│   │   ├── AddUser.tsx
│   │   ├── Competitors.tsx
│   │   ├── Dashboard.tsx
│   │   ├── DashboardClients.tsx
│   │   ├── DashboardUsers.tsx
│   │   ├── HtmlTemplateUpload.tsx
│   │   ├── Index.tsx
│   │   ├── Login.tsx
│   │   ├── NotFound.tsx
│   │   ├── Reports.tsx
│   │   └── TemplateEditor.tsx
│   ├── App.tsx             # Main application component
│   ├── main.tsx            # Application entry point
│   └── index.css           # Global styles
├── supabase/               # Supabase configuration
│   ├── functions/          # Serverless functions
│   └── migrations/         # Database migrations
├── .gitignore
├── components.json         # shadcn/ui configuration
├── eslint.config.js        # ESLint configuration
├── index.html              # HTML entry point
├── package.json            # Dependencies and scripts
├── postcss.config.js       # PostCSS configuration
├── tailwind.config.ts      # Tailwind CSS configuration
├── tsconfig.json           # TypeScript configuration
└── vite.config.ts          # Vite configuration
```

## 🔧 VS Code Setup Guide

### 1. **Prerequisites**
- Node.js & npm installed ([install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating))
- Git
- VS Code

### 2. **Recommended VS Code Extensions**
Install these extensions for the best development experience:

```json
{
  "recommendations": [
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "prisma.prisma",
    "formulahendry.auto-rename-tag",
    "steoates.autoimport-es6-ts-jsx-tsx",
    "mgmcdermott.vscode-language-babel",
    "burkeholland.simple-react-snippets",
    "dsznajder.es7-react-js-snippets",
    "christian-kohler.path-intellisense",
    "mikestead.dotenv",
    "yoavbls.pretty-ts-errors"
  ]
}
```

### 3. **VS Code Settings**
Create `.vscode/settings.json` in your project root:

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.updateImportsOnFileMove.enabled": "always",
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "files.associations": {
    "*.css": "tailwindcss"
  }
}
```

## 🛠️ Development Setup

### 1. **Clone and Install**
```bash
# Navigate to your project directory
cd D:\python\black-white-dashboard-main

# Install dependencies
npm install
```

### 2. **Environment Variables**
Create a `.env.local` file in the project root for your Supabase configuration:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. **Start Development Server**
```bash
npm run dev
```
The application will be available at `http://localhost:8080`

## 📝 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## 🏗️ Application Architecture

### **Routing Structure**
The application uses React Router with protected routes:

- `/` - Public landing page
- `/login` - Authentication page
- `/dashboard` - Main dashboard (protected)
- `/dashboard/users` - User management
- `/dashboard/clients` - Client management
- `/dashboard/reports` - Reporting section
- `/dashboard/competitors` - Competitor analysis
- `/admin/template-editor` - Template editing
- `/admin/html-template-upload` - HTML template upload

### **Authentication Flow**
- Uses Supabase for authentication
- Protected routes check authentication status
- Automatic redirect for authenticated/unauthenticated users

### **State Management**
- React Query for server state
- Custom hooks for authentication (`useAuth`)
- Form state managed by React Hook Form

## 🎨 Styling Guidelines

### **Tailwind CSS**
- Utility-first CSS framework
- Custom animations via `tailwindcss-animate`
- Typography plugin for rich text content

### **Component Library**
- shadcn/ui components (Radix UI based)
- Consistent theming through CSS variables
- Dark mode support via `next-themes`

## 🚀 Deployment

The project is configured for deployment on various platforms:

1. **Build the project**:
   ```bash
   npm run build
   ```

2. **Deploy the `dist` folder** to your hosting service

## 🔍 Key Features

1. **User Management** - Add, edit, and manage users
2. **Client Management** - Handle client information
3. **Reporting System** - Generate and view reports
4. **Competitor Analysis** - Track and analyze competitors
5. **Template Management** - Edit and upload HTML templates
6. **Authentication** - Secure login with Supabase
7. **Responsive Design** - Mobile-friendly interface

## 📚 Additional Resources

- [Vite Documentation](https://vitejs.dev/)
- [React Documentation](https://react.dev/)
- [TypeScript Documentation](https://www.typescriptlang.org/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Supabase Documentation](https://supabase.com/docs)

## 🤝 Migration Tips from Lovable to VS Code

1. **Git Integration**: Your project is already Git-enabled. Use VS Code's Source Control panel for commits.
2. **IntelliSense**: VS Code provides excellent TypeScript support out of the box.
3. **Debugging**: Configure VS Code's debugger for React applications.
4. **Terminal**: Use VS Code's integrated terminal for running npm commands.
5. **Extensions**: The recommended extensions will provide Lovable-like features.

## 🐛 Troubleshooting

### Common Issues:

1. **Port already in use**: Change the port in `vite.config.ts`
2. **TypeScript errors**: Run `npm run lint` to check for issues
3. **Missing environment variables**: Ensure `.env.local` is properly configured
4. **Build failures**: Clear `node_modules` and reinstall dependencies

---

*This documentation was generated to help transition from Lovable.dev to VS Code development.*