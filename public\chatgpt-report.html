<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>דוח ChatGPT Search - MAX</title>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: hsl(14, 100%, 60%);
            --secondary: hsl(36, 95%, 55%);
            --accent: hsl(176, 57%, 56%);
            --success: hsl(158, 68%, 75%);
            --warning: hsl(50, 100%, 60%);
            --danger: hsl(0, 79%, 70%);
            --muted: hsl(210, 40%, 15%);
            --background: hsl(210, 40%, 98%);
            --foreground: hsl(210, 40%, 8%);
            --card: hsl(0, 0%, 100%);
            --card-foreground: hsl(210, 40%, 8%);
            --border: hsl(214, 32%, 91%);
            --shadow: 0 10px 30px hsl(210, 40%, 8%, 0.1);
            --radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Heebo', sans-serif;
            background: hsl(var(--background));
            color: hsl(var(--foreground));
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            background: hsl(var(--card));
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            color: hsl(var(--primary));
            margin-bottom: 10px;
        }

        .header-subtitle {
            font-size: 1.2rem;
            color: hsl(var(--muted));
        }

        .section {
            background: hsl(var(--card));
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 30px;
            color: hsl(var(--primary));
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-title::before {
            content: '';
            width: 5px;
            height: 35px;
            background: hsl(var(--primary));
            border-radius: 3px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-box {
            background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .chatgpt-results-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .chatgpt-result-card {
            background: hsl(var(--background));
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .chatgpt-result-card:hover {
            border-color: hsl(var(--primary));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px hsl(210, 40%, 8%, 0.1);
        }

        .result-rank {
            font-size: 1.8rem;
            font-weight: bold;
            color: hsl(var(--primary));
            margin-bottom: 10px;
        }

        .result-question {
            font-size: 0.9rem;
            color: hsl(var(--muted));
            margin-bottom: 15px;
        }

        .result-badge {
            background: hsl(var(--success));
            color: hsl(var(--foreground));
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            display: inline-block;
        }

        .highlight-box {
            background: linear-gradient(135deg, hsl(var(--background)), hsl(var(--card)));
            padding: 20px;
            border-radius: var(--radius);
            border: 2px solid hsl(var(--primary));
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid hsl(var(--border));
        }

        .data-table th {
            background: hsl(var(--background));
            font-weight: 600;
            color: hsl(var(--foreground));
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tr:hover {
            background: hsl(var(--background));
        }

        .keyword-badge {
            background: hsl(var(--primary));
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .keyword-badge:hover {
            background: hsl(var(--secondary));
            transform: scale(1.05);
        }

        .chart-box {
            background: hsl(var(--card));
            padding: 30px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            height: 400px;
            margin-bottom: 30px;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: hsl(var(--foreground));
        }

        canvas {
            max-height: 300px !important;
        }

        @media (max-width: 768px) {
            .chatgpt-results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>ניתוח ChatGPT Search - MAX</h1>
            <p class="header-subtitle">נכון לתאריך: 24/06/2025 בשעה 13:57</p>
        </header>

        <!-- ChatGPT Statistics -->
        <section class="section">
            <h2 class="section-title">סטטיסטיקות כלליות</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <div class="stat-value">2,186</div>
                    <div class="stat-label">תוצאות נסרקו</div>
                </div>
                <div class="stat-box" style="background: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--success)));">
                    <div class="stat-value">42%</div>
                    <div class="stat-label">הופעות TOP 3</div>
                </div>
                <div class="stat-box" style="background: linear-gradient(135deg, hsl(var(--warning)), hsl(var(--secondary)));">
                    <div class="stat-value">5.2</div>
                    <div class="stat-label">דירוג ממוצע</div>
                </div>
                <div class="stat-box" style="background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--danger)));">
                    <div class="stat-value">56</div>
                    <div class="stat-label">הופעות כולל</div>
                </div>
            </div>
        </section>

        <!-- Category Breakdown -->
        <section class="section">
            <h2 class="section-title">ביצועים לפי קטגוריה</h2>
            <div class="chatgpt-results-grid">
                <div class="chatgpt-result-card">
                    <div class="result-rank">65%</div>
                    <div class="result-question">הלוואות</div>
                    <div class="result-badge" style="background: hsl(var(--success));">↑ 12% מהחודש שעבר</div>
                </div>
                <div class="chatgpt-result-card">
                    <div class="result-rank">20%</div>
                    <div class="result-question">ביטוח</div>
                    <div class="result-badge" style="background: hsl(var(--danger));">↓ 5% מהחודש שעבר</div>
                </div>
                <div class="chatgpt-result-card">
                    <div class="result-rank">15%</div>
                    <div class="result-question">כרטיסי אשראי</div>
                    <div class="result-badge" style="background: hsl(var(--danger));">↓ 3% מהחודש שעבר</div>
                </div>
            </div>
        </section>

        <!-- Key Findings -->
        <section class="section">
            <h2 class="section-title">סיכום מנהלים</h2>
            <div class="highlight-box">
                <h3 style="color: hsl(var(--primary)); margin-bottom: 20px;">ממצאים עיקריים:</h3>
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 10px 0; border-bottom: 1px solid hsl(var(--border));">
                        <strong>🏆 נוכחות חזקה:</strong> MAX מופיעה ב-56 מתוך 61 שאלות עם דירוגים גבוהים בקטגוריות מפתח
                    </li>
                    <li style="padding: 10px 0; border-bottom: 1px solid hsl(var(--border));">
                        <strong>📊 שליטה בהלוואות:</strong> ביצועים טובים בקטגוריית הלוואות עם 65% מהשאילתות
                    </li>
                    <li style="padding: 10px 0; border-bottom: 1px solid hsl(var(--border));">
                        <strong>⚡ זמינות תמורותי:</strong> דירוג ממוצע של 5.2 הופעות המיידיות, עם הופעות רבות ב-TOP 3
                    </li>
                    <li style="padding: 10px 0;">
                        <strong>📈 הזדמנויות צמיחה:</strong> פוטנציאל שיפור בקטגוריות ביטוח וכרטיסי אשראי
                    </li>
                </ul>
            </div>
        </section>

        <!-- Performance Chart -->
        <section class="section">
            <h2 class="section-title">מגמות ביצועים</h2>
            <div class="chart-box">
                <h3 class="chart-title">התפלגות ביצועים לפי קטגוריה</h3>
                <canvas id="performanceChart"></canvas>
            </div>
        </section>

        <!-- Top Performers Table -->
        <section class="section">
            <h2 class="section-title">מתחרים עיקריים</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>מתחרה</th>
                        <th>קטגוריה עיקרית</th>
                        <th>דירוג ממוצע</th>
                        <th>מספר הופעות</th>
                        <th>סטטוס</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>MAX</strong></td>
                        <td>הלוואות</td>
                        <td>5.2</td>
                        <td>56</td>
                        <td><span class="keyword-badge">מוביל</span></td>
                    </tr>
                    <tr>
                        <td>Cal Online</td>
                        <td>הלוואות</td>
                        <td>4.8</td>
                        <td>48</td>
                        <td><span class="keyword-badge" style="background: hsl(var(--warning));">מתחרה</span></td>
                    </tr>
                    <tr>
                        <td>Isracard</td>
                        <td>כרטיסי אשראי</td>
                        <td>5.5</td>
                        <td>42</td>
                        <td><span class="keyword-badge" style="background: hsl(var(--warning));">מתחרה</span></td>
                    </tr>
                    <tr>
                        <td>Bank Hapoalim</td>
                        <td>הלוואות</td>
                        <td>6.2</td>
                        <td>38</td>
                        <td><span class="keyword-badge" style="background: hsl(var(--success));">מתחרה</span></td>
                    </tr>
                    <tr>
                        <td>Harel</td>
                        <td>ביטוח</td>
                        <td>3.8</td>
                        <td>35</td>
                        <td><span class="keyword-badge" style="background: hsl(var(--success));">מתחרה</span></td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- Detailed Categories Analysis -->
        <section class="section">
            <h2 class="section-title">ניתוח מילות מפתח מובילות</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>מילת מפתח</th>
                        <th>קטגוריה</th>
                        <th>דירוג MAX</th>
                        <th>תדירות הופעה</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="keyword-badge">הלוואות לכל מטרה</span></td>
                        <td>הלוואות</td>
                        <td>3</td>
                        <td>גבוהה</td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">כרטיס אשראי קריפטו</span></td>
                        <td>כרטיסי אשראי</td>
                        <td>20</td>
                        <td>בינונית</td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">סליקת אשראי</span></td>
                        <td>תשלומים ופינטק</td>
                        <td>2</td>
                        <td>גבוהה</td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">חיסכון</span></td>
                        <td>כללי</td>
                        <td>3</td>
                        <td>בינונית</td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">ביטוח נסיעות</span></td>
                        <td>ביטוח</td>
                        <td>3</td>
                        <td>גבוהה</td>
                    </tr>
                </tbody>
            </table>
        </section>
    </div>

    <script>
        // Performance Chart
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['הלוואות', 'כרטיסי אשראי', 'ביטוח', 'תשלומים ופינטק', 'כללי'],
                    datasets: [{
                        label: 'MAX - מספר הופעות',
                        data: [36, 8, 3, 5, 4],
                        backgroundColor: 'hsl(14, 100%, 60%)',
                        borderColor: 'hsl(14, 100%, 50%)',
                        borderWidth: 1
                    }, {
                        label: 'מתחרים - ממוצע',
                        data: [28, 12, 8, 3, 2],
                        backgroundColor: 'hsl(176, 57%, 56%)',
                        borderColor: 'hsl(176, 57%, 46%)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'מספר הופעות',
                                font: {
                                    family: 'Heebo'
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'קטגוריות',
                                font: {
                                    family: 'Heebo'
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Heebo'
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>