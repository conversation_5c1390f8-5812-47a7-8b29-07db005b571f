<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>דוח AI Overview - MAX</title>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: hsl(14, 100%, 60%);
            --secondary: hsl(36, 95%, 55%);
            --accent: hsl(176, 57%, 56%);
            --success: hsl(158, 68%, 75%);
            --warning: hsl(50, 100%, 60%);
            --danger: hsl(0, 79%, 70%);
            --muted: hsl(210, 40%, 15%);
            --background: hsl(210, 40%, 98%);
            --foreground: hsl(210, 40%, 8%);
            --card: hsl(0, 0%, 100%);
            --card-foreground: hsl(210, 40%, 8%);
            --border: hsl(214, 32%, 91%);
            --shadow: 0 10px 30px hsl(210, 40%, 8%, 0.1);
            --radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Heebo', sans-serif;
            background: hsl(var(--background));
            color: hsl(var(--foreground));
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            background: hsl(var(--card));
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            color: hsl(var(--primary));
            margin-bottom: 10px;
        }

        .header-subtitle {
            font-size: 1.2rem;
            color: hsl(var(--muted));
        }

        .section {
            background: hsl(var(--card));
            padding: 40px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 30px;
            color: hsl(var(--primary));
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-title::before {
            content: '';
            width: 5px;
            height: 35px;
            background: hsl(var(--primary));
            border-radius: 3px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-box {
            background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid hsl(var(--border));
        }

        .data-table th {
            background: hsl(var(--background));
            font-weight: 600;
            color: hsl(var(--foreground));
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tr:hover {
            background: hsl(var(--background));
        }

        .keyword-badge {
            background: hsl(var(--primary));
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .keyword-badge:hover {
            background: hsl(var(--secondary));
            transform: scale(1.05);
        }

        .type-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
        }

        .type-badge.static {
            background: hsl(207, 89%, 95%);
            color: hsl(207, 89%, 35%);
        }

        .type-badge.live {
            background: hsl(300, 89%, 95%);
            color: hsl(300, 89%, 35%);
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .insight-card {
            background: hsl(var(--background));
            padding: 25px;
            border-radius: var(--radius);
            text-align: center;
            border-left: 4px solid hsl(var(--primary));
        }

        .insight-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .insight-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: hsl(var(--foreground));
            margin-bottom: 10px;
        }

        .insight-description {
            font-size: 0.9rem;
            color: hsl(var(--muted));
            line-height: 1.5;
        }

        .chart-box {
            background: hsl(var(--card));
            padding: 30px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            height: 400px;
            margin-bottom: 30px;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: hsl(var(--foreground));
        }

        canvas {
            max-height: 300px !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>דוח AI Overview - MAX</h1>
            <p class="header-subtitle">ניתוח מקיף של נוכחות MAX בתוצאות חיפוש AI - מאי-יוני 2025</p>
        </header>

        <!-- Key Insights -->
        <section class="section">
            <h2 class="section-title">תובנות מרכזיות</h2>
            <div class="insights-grid">
                <div class="insight-card">
                    <div class="insight-icon">📈</div>
                    <div class="insight-title">עליית הנוכחות בהלוואות</div>
                    <div class="insight-description">MAX שומרת על מיקום חזק בקטגוריית ההלוואות עם מגוון רחב של מוצרים</div>
                </div>
                <div class="insight-card">
                    <div class="insight-icon">🎯</div>
                    <div class="insight-title">מעמד חזק בכרטיסי אשראי</div>
                    <div class="insight-description">נוכחות בולטת במונחי חיפוש של כרטיסי אשראי וקריפטו</div>
                </div>
                <div class="insight-card">
                    <div class="insight-icon">💳</div>
                    <div class="insight-title">חדשנות בסליקה</div>
                    <div class="insight-description">פיתוח פתרונות סליקה מתקדמים לעסקים</div>
                </div>
            </div>
        </section>

        <!-- Statistics -->
        <section class="section">
            <h2 class="section-title">סטטיסטיקות כלליות</h2>
            <div class="stats-grid">
                <div class="stat-box">
                    <div class="stat-value">56</div>
                    <div class="stat-label">הופעות במאמרי AI</div>
                </div>
                <div class="stat-box" style="background: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--success)));">
                    <div class="stat-value">70%</div>
                    <div class="stat-label">Static AI</div>
                </div>
                <div class="stat-box" style="background: linear-gradient(135deg, hsl(var(--warning)), hsl(var(--secondary)));">
                    <div class="stat-value">30%</div>
                    <div class="stat-label">Live AI</div>
                </div>
                <div class="stat-box" style="background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--danger)));">
                    <div class="stat-value">120+</div>
                    <div class="stat-label">מילות מפתח פעילות</div>
                </div>
            </div>
        </section>

        <!-- Top Performing Keywords -->
        <section class="section">
            <h2 class="section-title">מילות מפתח מובילות</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>מילת מפתח</th>
                        <th>דירוג</th>
                        <th>קטגוריה</th>
                        <th>סוג AI</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="keyword-badge">הלוואה חוץ בנקאית לעסקים</span></td>
                        <td>2</td>
                        <td>הלוואות</td>
                        <td><span class="type-badge static">Static</span></td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">מימון לרכב</span></td>
                        <td>2</td>
                        <td>הלוואות</td>
                        <td><span class="type-badge static">Static</span></td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">ויזה</span></td>
                        <td>3</td>
                        <td>כרטיסי אשראי</td>
                        <td><span class="type-badge static">Static</span></td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">כרטיס אשראי נטען</span></td>
                        <td>3</td>
                        <td>כרטיסי אשראי</td>
                        <td><span class="type-badge static">Static</span></td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">סליקת אשראי</span></td>
                        <td>2</td>
                        <td>תשלומים ופינטק</td>
                        <td><span class="type-badge static">Static</span></td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">חיסכון</span></td>
                        <td>3</td>
                        <td>כללי</td>
                        <td><span class="type-badge static">Static</span></td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">קאשבק</span></td>
                        <td>12</td>
                        <td>כללי</td>
                        <td><span class="type-badge static">Static</span></td>
                    </tr>
                    <tr>
                        <td><span class="keyword-badge">קניית מט"ח באינטרנט</span></td>
                        <td>Live</td>
                        <td>כללי</td>
                        <td><span class="type-badge live">Live</span></td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- Category Performance Chart -->
        <section class="section">
            <h2 class="section-title">ביצועים לפי קטגוריה</h2>
            <div class="chart-box">
                <h3 class="chart-title">התפלגות הופעות לפי קטגוריה</h3>
                <canvas id="categoryChart"></canvas>
            </div>
        </section>

        <!-- Competitor Analysis -->
        <section class="section">
            <h2 class="section-title">ניתוח תחרותי</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>מתחרה</th>
                        <th>קטגוריה עיקרית</th>
                        <th>מספר הופעות</th>
                        <th>דירוג ממוצע</th>
                        <th>סטטוס</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>MAX</strong></td>
                        <td>הלוואות</td>
                        <td>56</td>
                        <td>5.2</td>
                        <td><span class="keyword-badge">מוביל</span></td>
                    </tr>
                    <tr>
                        <td>כאל</td>
                        <td>הלוואות</td>
                        <td>48</td>
                        <td>4.8</td>
                        <td><span class="keyword-badge" style="background: hsl(var(--warning));">מתחרה</span></td>
                    </tr>
                    <tr>
                        <td>ישראכרט</td>
                        <td>כרטיסי אשראי</td>
                        <td>42</td>
                        <td>5.5</td>
                        <td><span class="keyword-badge" style="background: hsl(var(--warning));">מתחרה</span></td>
                    </tr>
                    <tr>
                        <td>בנק הפועלים</td>
                        <td>הלוואות</td>
                        <td>38</td>
                        <td>6.2</td>
                        <td><span class="keyword-badge" style="background: hsl(var(--success));">מתחרה</span></td>
                    </tr>
                </tbody>
            </table>
        </section>
    </div>

    <script>
        // Category Performance Chart
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['הלוואות', 'כרטיסי אשראי', 'תשלומים ופינטק', 'כללי', 'ביטוח'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            'hsl(14, 100%, 60%)',
                            'hsl(36, 95%, 55%)',
                            'hsl(176, 57%, 56%)',
                            'hsl(158, 68%, 75%)',
                            'hsl(50, 100%, 60%)'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Heebo',
                                    size: 14
                                },
                                padding: 20
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>