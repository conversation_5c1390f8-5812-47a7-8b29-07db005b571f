import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { TopBar } from '@/components/layout/TopBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { FileText, Upload } from 'lucide-react';

interface Client {
  id: string;
  name: string;
}

const HtmlTemplateUpload = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { profile } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    clientId: '',
    htmlContent: ''
  });
  
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingClients, setLoadingClients] = useState(true);

  useEffect(() => {
    if (profile?.role !== 'admin') {
      navigate('/dashboard');
      return;
    }
    loadClients();
  }, [profile, navigate]);

  const loadClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setClients(data || []);
    } catch (error) {
      console.error('Error loading clients:', error);
      toast({
        title: "Error",
        description: "Failed to load clients",
        variant: "destructive",
      });
    } finally {
      setLoadingClients(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'text/html') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setFormData(prev => ({
          ...prev,
          htmlContent: content,
          name: prev.name || file.name.replace('.html', '')
        }));
      };
      reader.readAsText(file);
    } else {
      toast({
        title: "Invalid file",
        description: "Please upload an HTML file",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.clientId || !formData.htmlContent) {
      toast({
        title: "Missing fields",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from('html_templates')
        .insert({
          name: formData.name,
          description: formData.description,
          html_content: formData.htmlContent,
          client_id: formData.clientId,
          created_by: profile?.id
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "HTML template uploaded successfully",
      });

      navigate('/dashboard');
    } catch (error) {
      console.error('Error uploading template:', error);
      toast({
        title: "Error",
        description: "Failed to upload HTML template",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (profile?.role !== 'admin') {
    return null;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <div className="flex-1">
          <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          <Breadcrumbs />
          
          <main className="flex-1 p-6">
            <div className="max-w-2xl mx-auto space-y-6">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Upload HTML Template</h2>
                <p className="text-muted-foreground">
                  Upload custom HTML report templates for specific clients
                </p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    HTML Template Details
                  </CardTitle>
                  <CardDescription>
                    Upload an HTML file that will serve as a custom report template for a client
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="name">Template Name *</Label>
                      <Input
                        id="name"
                        placeholder="Enter template name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Optional description for this template"
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="client">Client *</Label>
                      <Select 
                        value={formData.clientId} 
                        onValueChange={(value) => handleInputChange('clientId', value)}
                        disabled={loadingClients}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={loadingClients ? "Loading clients..." : "Select a client"} />
                        </SelectTrigger>
                        <SelectContent>
                          {clients.map((client) => (
                            <SelectItem key={client.id} value={client.id}>
                              {client.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="htmlFile">HTML File *</Label>
                      <div className="flex items-center gap-4">
                        <Input
                          id="htmlFile"
                          type="file"
                          accept=".html,.htm"
                          onChange={handleFileUpload}
                          className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100"
                        />
                      </div>
                      {formData.htmlContent && (
                        <p className="text-sm text-green-600">
                          ✓ HTML file loaded ({Math.round(formData.htmlContent.length / 1024)}KB)
                        </p>
                      )}
                    </div>

                    {formData.htmlContent && (
                      <div className="space-y-2">
                        <Label>HTML Preview (first 500 characters)</Label>
                        <div className="p-3 bg-muted rounded-md">
                          <code className="text-sm">
                            {formData.htmlContent.substring(0, 500)}
                            {formData.htmlContent.length > 500 && '...'}
                          </code>
                        </div>
                      </div>
                    )}

                    <div className="flex gap-4 pt-4">
                      <Button
                        type="submit"
                        disabled={loading || !formData.name || !formData.clientId || !formData.htmlContent}
                        className="flex items-center gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        {loading ? 'Uploading...' : 'Upload Template'}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate('/dashboard')}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default HtmlTemplateUpload;