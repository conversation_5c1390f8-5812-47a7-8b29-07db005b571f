import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { format, subDays, startOfDay } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Upload, FileText } from 'lucide-react';

interface TrendData {
  date: string;
  reports: number;
  htmlTemplates: number;
  label: string;
}

export function ReportTrends() {
  const { data: trendData, isLoading } = useQuery({
    queryKey: ['report-trends'],
    queryFn: async () => {
      const days = 7;
      const endDate = new Date();
      const startDate = subDays(endDate, days - 1);

      // Get report data uploads
      const { data: reportData } = await supabase
        .from('report_data')
        .select('created_at')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      // Get HTML template uploads
      const { data: htmlTemplates } = await supabase
        .from('html_templates')
        .select('created_at')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      // Create data for each day
      const trends: TrendData[] = [];
      for (let i = 0; i < days; i++) {
        const currentDate = subDays(endDate, days - 1 - i);
        const dayStart = startOfDay(currentDate);
        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

        const reportsCount = reportData?.filter(report => {
          const reportDate = new Date(report.created_at);
          return reportDate >= dayStart && reportDate < dayEnd;
        }).length || 0;

        const templatesCount = htmlTemplates?.filter(template => {
          const templateDate = new Date(template.created_at);
          return templateDate >= dayStart && templateDate < dayEnd;
        }).length || 0;

        trends.push({
          date: format(currentDate, 'yyyy-MM-dd'),
          reports: reportsCount,
          htmlTemplates: templatesCount,
          label: format(currentDate, 'MMM dd')
        });
      }

      return trends;
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const { data: recentUploads } = useQuery({
    queryKey: ['recent-uploads'],
    queryFn: async () => {
      // Get recent report uploads
      const { data: reports } = await supabase
        .from('report_data')
        .select(`
          id,
          file_name,
          created_at,
          report_type,
          profiles:uploaded_by(first_name, last_name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(3);

      // Get recent HTML template uploads
      const { data: templates } = await supabase
        .from('html_templates')
        .select(`
          id,
          name,
          created_at,
          profiles:created_by(first_name, last_name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(3);

      const uploads = [
        ...(reports?.map(report => ({
          id: `report-${report.id}`,
          name: report.file_name || 'Unnamed Report',
          type: 'report',
          reportType: report.report_type,
          created_at: report.created_at,
          user: report.profiles ? 
            `${report.profiles.first_name || ''} ${report.profiles.last_name || ''}`.trim() || 
            report.profiles.email : 'Unknown'
        })) || []),
        ...(templates?.map(template => ({
          id: `template-${template.id}`,
          name: template.name,
          type: 'template',
          created_at: template.created_at,
          user: template.profiles ? 
            `${template.profiles.first_name || ''} ${template.profiles.last_name || ''}`.trim() || 
            template.profiles.email : 'Unknown'
        })) || [])
      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()).slice(0, 4);

      return uploads;
    },
    refetchInterval: 30000,
  });

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const past = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - past.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  if (isLoading) {
    return (
      <div className="h-[300px] flex items-center justify-center">
        <div className="animate-pulse text-muted-foreground">Loading trends...</div>
      </div>
    );
  }

  const totalUploads = trendData?.reduce((sum, day) => sum + day.reports + day.htmlTemplates, 0) || 0;
  const hasRecentActivity = (recentUploads?.length || 0) > 0;

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">{totalUploads} uploads this week</span>
        </div>
        {hasRecentActivity && (
          <Badge variant="secondary" className="text-xs">
            Live Updates
          </Badge>
        )}
      </div>

      {/* Chart */}
      <div className="h-[150px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={trendData}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="label" 
              fontSize={10}
              tick={{ fill: 'hsl(var(--muted-foreground))' }}
            />
            <YAxis 
              fontSize={10}
              tick={{ fill: 'hsl(var(--muted-foreground))' }}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'hsl(var(--popover))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '6px',
                fontSize: '12px'
              }}
            />
            <Bar 
              dataKey="reports" 
              fill="hsl(var(--primary))" 
              name="JSON Reports"
              radius={[2, 2, 0, 0]}
            />
            <Bar 
              dataKey="htmlTemplates" 
              fill="hsl(var(--secondary))" 
              name="HTML Templates"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Recent Uploads */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-muted-foreground">Latest Uploads</h4>
        <div className="space-y-2">
          {recentUploads?.map((upload) => (
            <div key={upload.id} className="flex items-center gap-2 text-xs">
              {upload.type === 'report' ? (
                <Upload className="h-3 w-3 text-primary" />
              ) : (
                <FileText className="h-3 w-3 text-secondary" />
              )}
              <span className="flex-1 truncate">{upload.name}</span>
              <span className="text-muted-foreground">{formatTimeAgo(upload.created_at)}</span>
            </div>
          )) || (
            <p className="text-xs text-muted-foreground text-center py-2">
              No recent uploads
            </p>
          )}
        </div>
      </div>
    </div>
  );
}