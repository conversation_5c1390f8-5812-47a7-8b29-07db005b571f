-- Add new fields to clients table
ALTER TABLE public.clients ADD COLUMN slug TEXT UNIQUE;
ALTER TABLE public.clients ADD COLUMN language TEXT DEFAULT 'english' CHECK (language IN ('english', 'hebrew'));
ALTER TABLE public.clients ADD COLUMN logo_url TEXT;
ALTER TABLE public.clients ADD COLUMN keywords TEXT[];

-- Create index for slug
CREATE INDEX idx_clients_slug ON public.clients(slug);

-- Update competitors table to store domain as primary field
ALTER TABLE public.competitors DROP COLUMN name;
ALTER TABLE public.competitors ALTER COLUMN domain SET NOT NULL;