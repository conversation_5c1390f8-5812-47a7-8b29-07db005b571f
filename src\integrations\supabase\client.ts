// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vqjjmelxpudzmnejakvo.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxamptZWx4cHVkem1uZWpha3ZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1MTE5MzUsImV4cCI6MjA2ODA4NzkzNX0.UsTPCXhXXZUnnBcx86ObkMkN_A28Ph4u3x-BBayJPq8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});