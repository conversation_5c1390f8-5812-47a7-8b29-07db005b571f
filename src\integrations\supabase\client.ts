// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ndhuzexembadathbfmmx.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kaHV6ZXhlbWJhZGF0aGJmbW14Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxNTIyODUsImV4cCI6MjA2NzcyODI4NX0._6V5fbtZ5VV_pW172FosrB9u3cb-MyC3Tz_lfQ128Cg";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});