-- Insert default report templates for both report types
INSERT INTO public.report_templates (name, description, template_content, report_type, is_default, created_by) 
VALUES 
(
  'Default Google AI Overview Template',
  'Standard template for Google AI Overview reports',
  '{
    "content": "<h1>{{client.name}} - AI Overview Report</h1>\n<p><strong>Report Period:</strong> {{report.date_from}} to {{report.date_to}}</p>\n<p><strong>Country:</strong> {{report.country}}</p>\n\n<h2>Executive Summary</h2>\n<p>This report analyzes the AI Overview presence for {{client.name}} across targeted search queries.</p>\n\n<h2>Search Query Analysis</h2>\n<p><strong>Query:</strong> {{data.search_query}}</p>\n\n<h2>AI Overview Content</h2>\n<div>\n<p><strong>Type:</strong> {{data.ai_overview.type}}</p>\n<p><strong>Content:</strong> {{data.ai_overview.content}}</p>\n</div>\n\n<h2>Sources Referenced</h2>\n<p>{{data.ai_overview.sources}}</p>\n\n<h2>Organic Results Summary</h2>\n<p>Total Results Found: {{data.total_results}}</p>\n<p>{{data.organic_results}}</p>\n\n<h2>Recommendations</h2>\n<p>Based on the analysis of AI Overview content for {{client.name}}, we recommend focusing on content optimization to improve visibility in AI-generated responses.</p>",
    "fields_used": ["client.name", "report.date_from", "report.date_to", "report.country", "data.search_query", "data.ai_overview.type", "data.ai_overview.content", "data.ai_overview.sources", "data.total_results", "data.organic_results"]
  }',
  'google_ai_overview',
  true,
  (SELECT id FROM public.profiles WHERE role = 'admin' LIMIT 1)
),
(
  'Default ChatGPT Results Template', 
  'Standard template for ChatGPT results reports',
  '{
    "content": "<h1>{{client.name}} - ChatGPT Analysis Report</h1>\n<p><strong>Report Period:</strong> {{report.date_from}} to {{report.date_to}}</p>\n<p><strong>Analysis Type:</strong> {{report.report_type}}</p>\n\n<h2>Overview</h2>\n<p>This report provides insights into ChatGPT search results and recommendations for {{client.name}}.</p>\n\n<h2>Search Analysis</h2>\n<p><strong>Query:</strong> {{data.search_query}}</p>\n<p><strong>Results Summary:</strong> {{data.search_information}}</p>\n\n<h2>Performance Metrics</h2>\n<p><strong>Visibility Score:</strong> {{metrics.visibility_score}}</p>\n<p><strong>Ranking Changes:</strong> {{metrics.ranking_changes}}</p>\n\n<h2>Competitive Analysis</h2>\n<p>{{metrics.competitor_analysis}}</p>\n\n<h2>Key Findings</h2>\n<p>{{data.organic_results}}</p>\n\n<h2>Strategic Recommendations</h2>\n<p>Based on the ChatGPT analysis for {{client.name}}, we recommend implementing targeted content strategies to improve AI search visibility.</p>",
    "fields_used": ["client.name", "report.date_from", "report.date_to", "report.report_type", "data.search_query", "data.search_information", "metrics.visibility_score", "metrics.ranking_changes", "metrics.competitor_analysis", "data.organic_results"]
  }',
  'chatgpt_results',
  true,
  (SELECT id FROM public.profiles WHERE role = 'admin' LIMIT 1)
);