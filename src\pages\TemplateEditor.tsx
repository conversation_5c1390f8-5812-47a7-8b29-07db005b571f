import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { 
  Save, 
  Eye, 
  Plus, 
  Trash2, 
  Copy, 
  Bold, 
  Italic, 
  List, 
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Type,
  Calendar,
  Building,
  BarChart3,
  Target,
  Globe,
  FileText,
  Edit
} from 'lucide-react';

interface Template {
  id: string;
  name: string;
  description: string;
  template_content: any;
  report_type: string;
  client_id: string | null;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

interface Client {
  id: string;
  name: string;
}

// Dynamic field categories and their available fields
const FIELD_CATEGORIES = {
  'Report Metadata': {
    icon: FileText,
    fields: [
      { name: 'report.date_from', label: 'Report Start Date', description: 'Starting date of the report period' },
      { name: 'report.date_to', label: 'Report End Date', description: 'Ending date of the report period' },
      { name: 'report.report_type', label: 'Report Type', description: 'Type of report (AI Overview, ChatGPT, etc.)' },
      { name: 'report.file_name', label: 'File Name', description: 'Original uploaded file name' },
      { name: 'report.country', label: 'Country', description: 'Country for the report data' },
      { name: 'report.traffic_type', label: 'Traffic Type', description: 'Type of traffic analyzed' }
    ]
  },
  'Client Information': {
    icon: Building,
    fields: [
      { name: 'client.name', label: 'Client Name', description: 'Name of the client' },
      { name: 'client.domain', label: 'Client Domain', description: 'Client\'s website domain' },
      { name: 'client.language', label: 'Language', description: 'Client\'s primary language' },
      { name: 'client.keywords', label: 'Keywords', description: 'Client\'s target keywords' }
    ]
  },
  'AI Overview Data': {
    icon: BarChart3,
    fields: [
      { name: 'data.ai_overview.content', label: 'AI Overview Content', description: 'Main AI Overview content' },
      { name: 'data.ai_overview.type', label: 'AI Overview Type', description: 'Type of AI Overview (live/static)' },
      { name: 'data.ai_overview.sources', label: 'AI Overview Sources', description: 'Sources used in AI Overview' },
      { name: 'data.search_query', label: 'Search Query', description: 'Original search query' }
    ]
  },
  'Search Results': {
    icon: Globe,
    fields: [
      { name: 'data.organic_results', label: 'Organic Results', description: 'Organic search results' },
      { name: 'data.total_results', label: 'Total Results', description: 'Total number of search results' },
      { name: 'data.search_information', label: 'Search Information', description: 'General search metadata' }
    ]
  },
  'Performance Metrics': {
    icon: Target,
    fields: [
      { name: 'metrics.visibility_score', label: 'Visibility Score', description: 'Overall visibility score' },
      { name: 'metrics.ranking_changes', label: 'Ranking Changes', description: 'Changes in rankings' },
      { name: 'metrics.competitor_analysis', label: 'Competitor Analysis', description: 'Competitor performance data' }
    ]
  }
};

export default function TemplateEditor() {
  const { profile } = useAuth();
  const { toast } = useToast();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(false);
  
  // Form state
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [reportType, setReportType] = useState('google_ai_overview');
  const [clientId, setClientId] = useState<string | null>(null);
  const [isDefault, setIsDefault] = useState(false);

  // Template content state
  const [templateContent, setTemplateContent] = useState('');

  // Check if user is admin
  if (profile?.role !== 'admin') {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="flex items-center justify-center p-8">
            <p className="text-muted-foreground">Access denied. Admin privileges required.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  console.log('Current state:', { 
    selectedTemplate: selectedTemplate?.name, 
    isCreating, 
    templates: templates.length,
    templateContent: templateContent.substring(0, 50) + '...'
  });

  // Load templates and clients
  useEffect(() => {
    loadTemplates();
    loadClients();
  }, []);

  const loadTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('report_templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Error loading templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to load templates',
        variant: 'destructive',
      });
    }
  };

  const loadClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setClients(data || []);
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  const handleNewTemplate = () => {
    setIsCreating(true);
    setSelectedTemplate(null);
    setTemplateName('');
    setTemplateDescription('');
    setReportType('google_ai_overview');
    setClientId(null);
    setIsDefault(false);
    setTemplateContent('');
  };

  const handleSelectTemplate = (template: Template) => {
    console.log('Selected template:', template);
    console.log('Template content structure:', template.template_content);
    
    try {
      setSelectedTemplate(template);
      setIsCreating(false);
      setTemplateName(template.name);
      setTemplateDescription(template.description || '');
      setReportType(template.report_type as any);
      setClientId(template.client_id);
      setIsDefault(template.is_default);
      
      // Safely access template content
      const content = typeof template.template_content === 'string' 
        ? template.template_content 
        : template.template_content?.content || '';
      
      console.log('Setting content:', content);
      setTemplateContent(content);
    } catch (error) {
      console.error('Error selecting template:', error);
      toast({
        title: 'Error',
        description: 'Failed to load template',
        variant: 'destructive',
      });
    }
  };

  const handleSaveTemplate = async () => {
    if (!templateName.trim()) {
      toast({
        title: 'Error',
        description: 'Template name is required',
        variant: 'destructive',
      });
      return;
    }

    if (!templateContent.trim()) {
      toast({
        title: 'Error',
        description: 'Template content is required',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      const templateData = {
        name: templateName,
        description: templateDescription,
        template_content: {
          content: templateContent,
          fields_used: extractFieldsFromContent(templateContent)
        },
        report_type: reportType,
        client_id: clientId,
        is_default: isDefault,
        created_by: profile?.id
      };

      if (selectedTemplate && !isCreating) {
        // Update existing template
        const { error } = await supabase
          .from('report_templates')
          .update(templateData)
          .eq('id', selectedTemplate.id);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Template updated successfully',
        });
      } else {
        // Create new template
        const { error } = await supabase
          .from('report_templates')
          .insert(templateData);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Template created successfully',
        });
      }

      await loadTemplates();
      setIsCreating(false);
    } catch (error) {
      console.error('Error saving template:', error);
      toast({
        title: 'Error',
        description: 'Failed to save template',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    try {
      const { error } = await supabase
        .from('report_templates')
        .delete()
        .eq('id', templateId);

      if (error) throw error;
      
      toast({
        title: 'Success',
        description: 'Template deleted successfully',
      });
      
      await loadTemplates();
      if (selectedTemplate?.id === templateId) {
        setSelectedTemplate(null);
        setIsCreating(false);
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete template',
        variant: 'destructive',
      });
    }
  };

  const insertField = (fieldPath: string) => {
    const fieldTag = `{{${fieldPath}}}`;
    setTemplateContent(prev => prev + ` ${fieldTag} `);
  };

  const extractFieldsFromContent = (content: string): string[] => {
    const fieldRegex = /\{\{([^}]+)\}\}/g;
    const matches = content.match(fieldRegex) || [];
    return matches.map(match => match.replace(/[{}]/g, ''));
  };

  const formatEditorText = (command: string) => {
    // Simple text formatting for textarea - can be enhanced later
    console.log('Format command:', command);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Template Editor</h1>
          <p className="text-muted-foreground">Manage report templates - edit existing ones or create new custom templates</p>
        </div>
        <Button onClick={handleNewTemplate} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Template
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Template List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Templates
              <Badge variant="secondary">{templates.length}</Badge>
            </CardTitle>
            <CardDescription>
              {templates.length > 0 ? 'Select a template to edit or create a new one' : 'No templates found. Create your first template.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {templates.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mb-4">No templates found</p>
                <Button onClick={handleNewTemplate} size="sm" className="w-full">
                  Create First Template
                </Button>
              </div>
            ) : (
              <ScrollArea className="h-[400px]">
                <div className="space-y-2">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className={`group p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedTemplate?.id === template.id
                          ? 'bg-primary/10 border-primary'
                          : 'hover:bg-muted/50'
                      }`}
                      onClick={() => handleSelectTemplate(template)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium truncate">{template.name}</h4>
                          <p className="text-sm text-muted-foreground truncate">
                            {template.description || 'No description'}
                          </p>
                          <div className="flex gap-1 mt-2">
                            <Badge variant="secondary" className="text-xs">
                              {template.report_type.replace('_', ' ')}
                            </Badge>
                            {template.is_default && (
                              <Badge variant="default" className="text-xs">Default</Badge>
                            )}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTemplate(template.id);
                          }}
                          className="opacity-0 group-hover:opacity-100 ml-2 h-8 w-8 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </CardContent>
        </Card>

        {/* Editor */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {isCreating ? (
                <>
                  <Plus className="h-5 w-5" />
                  Create New Template
                </>
              ) : selectedTemplate ? (
                <>
                  <Edit className="h-5 w-5" />
                  Edit Template: {selectedTemplate.name}
                </>
              ) : (
                <>
                  <Type className="h-5 w-5" />
                  Select a Template
                </>
              )}
            </CardTitle>
            <CardDescription>
              {isCreating || selectedTemplate ? 'Design your report template with dynamic fields' : 'Choose a template from the list to start editing'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {(isCreating || selectedTemplate) && (
              <>
                {/* Template Settings */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Template Name</Label>
                    <Input
                      id="name"
                      value={templateName}
                      onChange={(e) => setTemplateName(e.target.value)}
                      placeholder="Enter template name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="type">Report Type</Label>
                    <Select value={reportType} onValueChange={(value: any) => setReportType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="google_ai_overview">Google AI Overview</SelectItem>
                        <SelectItem value="chatgpt_results">ChatGPT Results</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={templateDescription}
                    onChange={(e) => setTemplateDescription(e.target.value)}
                    placeholder="Describe this template"
                    rows={2}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="client">Client (Optional)</Label>
                    <Select value={clientId || ''} onValueChange={(value) => setClientId(value || null)}>
                      <SelectTrigger>
                        <SelectValue placeholder="All clients" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All clients</SelectItem>
                        {clients.map((client) => (
                          <SelectItem key={client.id} value={client.id}>
                            {client.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2 pt-6">
                    <input
                      type="checkbox"
                      id="default"
                      checked={isDefault}
                      onChange={(e) => setIsDefault(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="default">Set as default template</Label>
                  </div>
                </div>

                <Separator />

                {/* Template Content Editor */}
                <div>
                  <Label htmlFor="content">Template Content</Label>
                  <Textarea
                    id="content"
                    value={templateContent}
                    onChange={(e) => setTemplateContent(e.target.value)}
                    placeholder="Start typing your template content... Use the field buttons on the right to insert dynamic data like {{client.name}} or {{report.date_from}}"
                    rows={15}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Use dynamic fields like {`{{client.name}}`}, {`{{report.date_from}}`}, etc. Click the field buttons on the right to insert them automatically.
                  </p>
                </div>

                {/* Save Button */}
                <div className="flex gap-2">
                  <Button onClick={handleSaveTemplate} disabled={loading} className="flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    {loading ? 'Saving...' : 'Save Template'}
                  </Button>
                  {selectedTemplate && (
                    <Button variant="outline" className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Preview
                    </Button>
                  )}
                </div>
              </>
            )}

            {!isCreating && !selectedTemplate && (
              <div className="text-center py-12 text-muted-foreground">
                <Type className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Select a Template to Edit</h3>
                <p className="mb-4">Choose a template from the list on the left to start editing, or create a new one</p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={handleNewTemplate} variant="default">
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Template
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Dynamic Fields */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Dynamic Fields</CardTitle>
            <CardDescription>Click to insert field into template</CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px]">
              <div className="space-y-4">
                {Object.entries(FIELD_CATEGORIES).map(([category, { icon: Icon, fields }]) => (
                  <div key={category} className="space-y-2">
                    <div className="flex items-center gap-2 font-medium text-sm">
                      <Icon className="h-4 w-4" />
                      {category}
                    </div>
                    <div className="space-y-1 pl-6">
                      {fields.map((field) => (
                        <Button
                          key={field.name}
                          variant="ghost"
                          size="sm"
                          className="w-full justify-start text-left h-auto p-2"
                          onClick={() => insertField(field.name)}
                        >
                          <div>
                            <div className="font-medium text-xs">{field.label}</div>
                            <div className="text-xs text-muted-foreground">{field.description}</div>
                            <code className="text-xs bg-muted px-1 rounded">{`{{${field.name}}}`}</code>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}