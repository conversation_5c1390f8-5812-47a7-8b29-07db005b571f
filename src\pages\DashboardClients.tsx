import { useState } from 'react';
import { <PERSON>barProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { TopBar } from '@/components/layout/TopBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Plus, Search, MoreHorizontal, Building, Edit, Trash2, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

const DashboardClients = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [localSearch, setLocalSearch] = useState('');
  const [editingClient, setEditingClient] = useState<any>(null);
  const [deletingClient, setDeletingClient] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    language: 'english',
    domain: '',
    logoUrl: '',
  });
  const [competitors, setCompetitors] = useState<string[]>(['']);
  const [keywords, setKeywords] = useState<string[]>(['']);
  
  const { profile } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const isAdmin = profile?.role === 'admin';

  const { data: clients, isLoading } = useQuery({
    queryKey: ['clients'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('clients')
        .select(`
          *,
          profiles(id),
          competitors(id)
        `)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  const filteredClients = clients?.filter(client =>
    client.name?.toLowerCase().includes(localSearch.toLowerCase()) ||
    client.domain?.toLowerCase().includes(localSearch.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleEditClick = async (client: any) => {
    setEditingClient(client);
    setFormData({
      name: client.name,
      slug: client.slug || '',
      language: client.language || 'english',
      domain: client.domain || '',
      logoUrl: client.logo_url || '',
    });
    
    // Fetch current competitors and keywords
    try {
      const { data: competitorsData } = await supabase
        .from('competitors')
        .select('domain')
        .eq('client_id', client.id);
      
      setCompetitors(competitorsData?.map(c => c.domain) || ['']);
      setKeywords(client.keywords || ['']);
    } catch (error) {
      console.error('Error fetching client details:', error);
      setCompetitors(['']);
      setKeywords(['']);
    }
  };

  const handleDeleteClick = (client: any) => {
    setDeletingClient(client);
  };

  const handleUpdateClient = async () => {
    if (!editingClient) return;
    
    setLoading(true);
    try {
      // Update client
      const { error: clientError } = await supabase
        .from('clients')
        .update({
          name: formData.name,
          slug: formData.slug,
          language: formData.language,
          domain: formData.domain,
          logo_url: formData.logoUrl || null,
          keywords: keywords.filter(k => k.trim()),
        })
        .eq('id', editingClient.id);

      if (clientError) throw clientError;

      // Delete existing competitors
      await supabase
        .from('competitors')
        .delete()
        .eq('client_id', editingClient.id);

      // Add new competitors
      const validCompetitors = competitors.filter(c => c.trim());
      if (validCompetitors.length > 0) {
        const { error: competitorsError } = await supabase
          .from('competitors')
          .insert(
            validCompetitors.map(domain => ({
              client_id: editingClient.id,
              domain: domain.trim(),
            }))
          );

        if (competitorsError) throw competitorsError;
      }

      toast({
        title: "Client updated",
        description: "Client has been updated successfully.",
      });

      setEditingClient(null);
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    } catch (error) {
      console.error('Error updating client:', error);
      toast({
        title: "Error",
        description: `Failed to update client: ${error?.message || 'Please try again.'}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async () => {
    if (!deletingClient) return;
    
    setLoading(true);
    try {
      // Delete competitors first
      await supabase
        .from('competitors')
        .delete()
        .eq('client_id', deletingClient.id);

      // Delete client
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', deletingClient.id);

      if (error) throw error;

      toast({
        title: "Client deleted",
        description: "Client has been deleted successfully.",
      });

      setDeletingClient(null);
      queryClient.invalidateQueries({ queryKey: ['clients'] });
    } catch (error) {
      console.error('Error deleting client:', error);
      toast({
        title: "Error",
        description: `Failed to delete client: ${error?.message || 'Please try again.'}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const addCompetitor = () => {
    if (competitors.length < 10) {
      setCompetitors([...competitors, '']);
    }
  };

  const removeCompetitor = (index: number) => {
    if (competitors.length > 1) {
      setCompetitors(competitors.filter((_, i) => i !== index));
    }
  };

  const updateCompetitor = (index: number, value: string) => {
    const updated = [...competitors];
    updated[index] = value;
    setCompetitors(updated);
  };

  const addKeyword = () => {
    setKeywords([...keywords, '']);
  };

  const removeKeyword = (index: number) => {
    setKeywords(keywords.filter((_, i) => i !== index));
  };

  const updateKeyword = (index: number, value: string) => {
    const updated = [...keywords];
    updated[index] = value;
    setKeywords(updated);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <div className="flex-1">
          <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          <Breadcrumbs />
          
          <main className="flex-1 p-6">
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">Clients</h2>
                  <p className="text-muted-foreground">
                    Manage your client organizations and their settings.
                  </p>
                </div>
                <Button asChild>
                  <Link to="/dashboard/clients/add">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Client
                  </Link>
                </Button>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>All Clients</CardTitle>
                  <CardDescription>
                    A list of all client organizations in your system.
                  </CardDescription>
                  <div className="relative w-full max-w-sm">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Search clients..."
                      value={localSearch}
                      onChange={(e) => setLocalSearch(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="text-center py-8">Loading clients...</div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Domain</TableHead>
                          <TableHead>Users</TableHead>
                          <TableHead>Competitors</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead className="w-[70px]"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredClients?.map((client) => (
                          <TableRow key={client.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <Building className="h-4 w-4 text-muted-foreground" />
                                {client.name}
                              </div>
                            </TableCell>
                            <TableCell>
                              {client.domain ? (
                                <a 
                                  href={`https://${client.domain}`} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  {client.domain}
                                </a>
                              ) : (
                                <span className="text-muted-foreground">No domain</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <span className="inline-flex items-center rounded-full bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground">
                                {client.profiles?.length || 0} users
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className="inline-flex items-center rounded-full bg-muted px-2 py-1 text-xs font-medium text-muted-foreground">
                                {client.competitors?.length || 0} competitors
                              </span>
                            </TableCell>
                            <TableCell className="text-muted-foreground">
                              {formatDate(client.created_at)}
                            </TableCell>
                            <TableCell>
                              {isAdmin ? (
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => handleEditClick(client)}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit Client
                                    </DropdownMenuItem>
                                    <DropdownMenuItem 
                                      onClick={() => handleDeleteClick(client)}
                                      className="text-destructive focus:text-destructive"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete Client
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              ) : (
                                <Button variant="ghost" size="icon" disabled>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                  
                  {!isLoading && (!filteredClients || filteredClients.length === 0) && (
                    <div className="text-center py-8 text-muted-foreground">
                      {localSearch ? 'No clients found matching your search.' : 'No clients found.'}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>

      {/* Edit Client Dialog */}
      <Dialog open={!!editingClient} onOpenChange={() => setEditingClient(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Client</DialogTitle>
            <DialogDescription>
              Update the client information and settings.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Client Name *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter client name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-slug">Slug *</Label>
                <Input
                  id="edit-slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="client-slug"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-language">Report Language</Label>
                <Select value={formData.language} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, language: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="english">English</SelectItem>
                    <SelectItem value="hebrew">Hebrew</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-domain">Domain *</Label>
                <Input
                  id="edit-domain"
                  value={formData.domain}
                  onChange={(e) => setFormData(prev => ({ ...prev, domain: e.target.value }))}
                  placeholder="example.com"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>Competitors * (minimum 1, maximum 10)</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addCompetitor}
                  disabled={competitors.length >= 10}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
              {competitors.map((competitor, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={competitor}
                    onChange={(e) => updateCompetitor(index, e.target.value)}
                    placeholder="competitor-domain.com"
                  />
                  {competitors.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeCompetitor(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Label>Top Keywords</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addKeyword}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
              {keywords.map((keyword, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={keyword}
                    onChange={(e) => updateKeyword(index, e.target.value)}
                    placeholder="keyword"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => removeKeyword(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditingClient(null)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateClient} disabled={loading}>
              {loading ? 'Updating...' : 'Update Client'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Client Confirmation */}
      <AlertDialog open={!!deletingClient} onOpenChange={() => setDeletingClient(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Client</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingClient?.name}"? This action cannot be undone.
              All related data including competitors and reports will be permanently removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteClient}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={loading}
            >
              {loading ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </SidebarProvider>
  );
};

export default DashboardClients;