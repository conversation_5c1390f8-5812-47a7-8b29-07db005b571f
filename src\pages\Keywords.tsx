import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Loader2, Search, Download, Upload, TrendingUp } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { KeywordUpload } from "@/components/admin/KeywordUpload";
import { useToast } from "@/hooks/use-toast";

interface Keyword {
  id: string;
  keyword: string;
  volume: number;
  difficulty?: number;
  cpc?: number;
  client_id: string;
  client_name: string;
  created_at: string;
}

interface Client {
  id: string;
  name: string;
}

export default function Keywords() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedClient, setSelectedClient] = useState<string>("all");
  const [showUpload, setShowUpload] = useState(false);
  const { profile } = useAuth();
  const { toast } = useToast();

  // Fetch clients
  const { data: clients } = useQuery({
    queryKey: ["clients"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("clients")
        .select("id, name")
        .order("name");
      
      if (error) throw error;
      return data as Client[];
    },
  });

  // Fetch keywords with client information
  const { data: keywords, isLoading, refetch } = useQuery({
    queryKey: ["keywords", selectedClient, searchTerm],
    queryFn: async () => {
      let query = supabase
        .from("keywords")
        .select(`
          id,
          keyword,
          volume,
          difficulty,
          cpc,
          client_id,
          created_at,
          clients!inner(name)
        `)
        .order("volume", { ascending: false });

      if (selectedClient !== "all") {
        query = query.eq("client_id", selectedClient);
      }

      if (searchTerm) {
        query = query.ilike("keyword", `%${searchTerm}%`);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      return data.map(item => ({
        id: item.id,
        keyword: item.keyword,
        volume: item.volume,
        difficulty: item.difficulty,
        cpc: item.cpc,
        client_id: item.client_id,
        client_name: (item.clients as any).name,
        created_at: item.created_at,
      })) as Keyword[];
    },
  });

  const exportKeywords = () => {
    if (!keywords || keywords.length === 0) {
      toast({
        title: "No data to export",
        description: "There are no keywords to export.",
        variant: "destructive",
      });
      return;
    }

    const csvContent = [
      "keyword,volume,difficulty,cpc,client,created_at",
      ...keywords.map(kw => 
        `"${kw.keyword}",${kw.volume},${kw.difficulty || ''},${kw.cpc || ''},"${kw.client_name}",${kw.created_at}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `keywords_export_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const getDifficultyColor = (difficulty?: number) => {
    if (!difficulty) return "secondary";
    if (difficulty < 0.3) return "default";
    if (difficulty < 0.6) return "secondary";
    return "destructive";
  };

  const getDifficultyLabel = (difficulty?: number) => {
    if (!difficulty) return "Unknown";
    if (difficulty < 0.3) return "Easy";
    if (difficulty < 0.6) return "Medium";
    return "Hard";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Keywords</h1>
          <p className="text-muted-foreground">
            Manage and analyze keyword data with search volumes
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportKeywords}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          {profile?.role === 'admin' && (
            <Button onClick={() => setShowUpload(!showUpload)}>
              <Upload className="h-4 w-4 mr-2" />
              Upload Keywords
            </Button>
          )}
        </div>
      </div>

      {/* Upload Component - Only for Admins */}
      {showUpload && profile?.role === 'admin' && (
        <KeywordUpload />
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search keywords..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="w-48">
              <Select value={selectedClient} onValueChange={setSelectedClient}>
                <SelectTrigger>
                  <SelectValue placeholder="All clients" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Clients</SelectItem>
                  {clients?.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Keywords Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Keywords Data
          </CardTitle>
          <CardDescription>
            {keywords ? `${keywords.length} keywords found` : 'Loading keywords...'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading keywords...</span>
            </div>
          ) : keywords && keywords.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Keyword</TableHead>
                    <TableHead>Volume</TableHead>
                    <TableHead>Difficulty</TableHead>
                    <TableHead>CPC</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Added</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {keywords.map((keyword) => (
                    <TableRow key={keyword.id}>
                      <TableCell className="font-medium">
                        {keyword.keyword}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4 text-muted-foreground" />
                          {formatNumber(keyword.volume)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {keyword.difficulty ? (
                          <Badge variant={getDifficultyColor(keyword.difficulty)}>
                            {getDifficultyLabel(keyword.difficulty)} ({keyword.difficulty.toFixed(2)})
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {keyword.cpc ? (
                          <span>${keyword.cpc.toFixed(2)}</span>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{keyword.client_name}</Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {new Date(keyword.created_at).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8">
              <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium">No keywords found</h3>
              <p className="text-muted-foreground">
                {profile?.role === 'admin' 
                  ? "Upload some keyword data to get started."
                  : "No keyword data available yet."
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
