-- Create report_templates table for admin-customizable report templates
CREATE TABLE public.report_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  template_content JSONB NOT NULL,
  report_type TEXT NOT NULL CHECK (report_type IN ('google_ai_overview', 'chatgpt_results')),
  client_id UUID REFERENCES public.clients(id),
  is_default BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID NOT NULL REFERENCES public.profiles(id)
);

-- Enable Row Level Security
ALTER TABLE public.report_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for admin-only access
CREATE POLICY "<PERSON><PERSON> can manage all templates" 
ON public.report_templates 
FOR ALL 
USING (get_user_role() = 'admin'::user_role);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_report_templates_updated_at
BEFORE UPDATE ON public.report_templates
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create index for efficient template retrieval
CREATE INDEX idx_report_templates_type_client ON public.report_templates(report_type, client_id);
CREATE INDEX idx_report_templates_default ON public.report_templates(is_default) WHERE is_default = true;