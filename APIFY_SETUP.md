# Apify Integration Setup Guide

## Overview
The BrandAI dashboard includes an Apify integration feature that allows fetching data from Apify tasks and storing them as reports. This feature is currently disabled and requires proper configuration.

## Current Status
❌ **Disabled** - The Apify integration is temporarily disabled to prevent deployment errors.

## Required Configuration

### 1. Supabase Edge Function
The `fetch-apify-data` function needs to be deployed to your Supabase project:

1. **Deploy the function** to Supabase:
   ```bash
   supabase functions deploy fetch-apify-data
   ```

2. **Set environment variables** in Supabase:
   - `APIFY_API_TOKEN` - Your Apify API token
   - `SUPABASE_URL` - Your Supabase project URL
   - `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key

### 2. Apify API Token
1. **Get your Apify API token**:
   - Go to [Apify Console](https://console.apify.com/)
   - Navigate to Settings > Integrations
   - Copy your API token

2. **Configure in Supabase**:
   - Go to your Supabase project dashboard
   - Navigate to Edge Functions
   - Set the `APIFY_API_TOKEN` environment variable

### 3. Enable the Integration
Once configured, update the `ApifyIntegration.tsx` component:

1. **Remove the temporary disable code**:
   ```typescript
   // Remove this block:
   toast({
     title: "Feature Configuration Required",
     description: "Apify integration needs to be configured...",
     variant: "destructive",
   });
   return;
   ```

2. **Uncomment the actual integration code**:
   ```typescript
   // Uncomment the fetch logic in handleFetchData function
   ```

## How It Works
1. **User selects** a client and Apify task
2. **Frontend calls** the Supabase Edge Function
3. **Function fetches** data from Apify API
4. **Data is stored** in the `report_data` table
5. **Reports appear** in the dashboard based on task type

## Task Types Supported
- `google_ai_overview` - Google AI Overview reports
- `chatgpt_results` - ChatGPT analysis reports
- `brand_ai_overview` - Brand AI analysis reports

## Security Notes
- The Apify API token should never be exposed to the frontend
- All API calls go through the secure Supabase Edge Function
- User authentication is required for all operations

## Testing
1. **Deploy the function** with proper environment variables
2. **Test with a simple task** to verify connectivity
3. **Check the `report_data` table** for stored results
4. **Verify reports** appear in the dashboard

## Troubleshooting
- **"APIFY_API_TOKEN not configured"** - Set the environment variable in Supabase
- **"Function not found"** - Deploy the Edge Function to Supabase
- **"Invalid user"** - Check authentication and user profile setup
- **"Database error"** - Verify RLS policies and table permissions

## Future Enhancements
- Add support for custom Apify actors
- Implement data transformation pipelines
- Add scheduling for automatic data fetching
- Include data validation and error recovery
