import { useState } from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { TopBar } from '@/components/layout/TopBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import { OverviewCards } from '@/components/dashboard/OverviewCards';
import { RecentActivity } from '@/components/dashboard/RecentActivity';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ReportTrends } from '@/components/dashboard/ReportTrends';

const Dashboard = () => {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <div className="flex-1">
          <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          <Breadcrumbs />
          
          <main className="flex-1 p-6">
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Dashboard Overview</h2>
                <p className="text-muted-foreground">
                  Monitor your reports, users, and analytics at a glance.
                </p>
              </div>
              
              <OverviewCards />
              
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
                <div className="col-span-4">
                  <RecentActivity />
                </div>
                <div className="col-span-3">
                  <Card>
                    <CardHeader>
                      <CardTitle>Report Upload Trends</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ReportTrends />
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Dashboard;