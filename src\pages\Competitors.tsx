import { useState, useEffect } from 'react';
import { <PERSON>bar<PERSON><PERSON>ider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { TopBar } from '@/components/layout/TopBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Plus, Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';

interface Client {
  id: string;
  name: string;
  domain: string;
  slug: string;
}

interface Competitor {
  id: string;
  domain: string;
  client_id: string;
  created_at: string;
  active?: boolean;
}

const Competitors = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [clients, setClients] = useState<Client[]>([]);
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(true);
  const [addingCompetitor, setAddingCompetitor] = useState(false);
  const [newCompetitorDomain, setNewCompetitorDomain] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  
  const { profile } = useAuth();
  const { toast } = useToast();

  const isAdminOrEditor = profile?.role === 'admin' || profile?.role === 'editor';
  const isViewer = profile?.role === 'view';

  useEffect(() => {
    fetchData();
  }, [profile]);

  const fetchData = async () => {
    if (!profile) return;

    setLoading(true);
    try {
      if (isAdminOrEditor) {
        // Fetch all clients for admin/editor
        const { data: clientsData, error: clientsError } = await supabase
          .from('clients')
          .select('*')
          .order('name');

        if (clientsError) throw clientsError;
        setClients(clientsData || []);
      } else if (isViewer) {
        // Fetch competitors for viewer's client only
        const { data: competitorsData, error: competitorsError } = await supabase
          .from('competitors')
          .select('*')
          .order('domain');

        if (competitorsError) throw competitorsError;
        setCompetitors(competitorsData || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCompetitors = async (clientId: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('competitors')
        .select('*')
        .eq('client_id', clientId)
        .order('domain');

      if (error) throw error;
      setCompetitors(data || []);
    } catch (error) {
      console.error('Error fetching competitors:', error);
      toast({
        title: "Error",
        description: "Failed to fetch competitors. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    fetchCompetitors(client.id);
  };

  const handleAddCompetitor = async () => {
    if (!newCompetitorDomain.trim() || !selectedClient) return;

    setAddingCompetitor(true);
    try {
      const { error } = await supabase
        .from('competitors')
        .insert({
          client_id: selectedClient.id,
          domain: newCompetitorDomain.trim(),
        });

      if (error) throw error;

      toast({
        title: "Competitor added",
        description: "Competitor has been added successfully.",
      });

      setNewCompetitorDomain('');
      setIsAddDialogOpen(false);
      fetchCompetitors(selectedClient.id);
    } catch (error) {
      console.error('Error adding competitor:', error);
      toast({
        title: "Error",
        description: "Failed to add competitor. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAddingCompetitor(false);
    }
  };

  const handleDeleteCompetitor = async (competitorId: string) => {
    try {
      const { error } = await supabase
        .from('competitors')
        .delete()
        .eq('id', competitorId);

      if (error) throw error;

      toast({
        title: "Competitor deleted",
        description: "Competitor has been deleted successfully.",
      });

      if (selectedClient) {
        fetchCompetitors(selectedClient.id);
      } else {
        fetchData();
      }
    } catch (error) {
      console.error('Error deleting competitor:', error);
      toast({
        title: "Error",
        description: "Failed to delete competitor. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleToggleCompetitor = async (competitorId: string, active: boolean) => {
    // This would typically update a status field in the database
    // For now, we'll just show the toast
    toast({
      title: active ? "Competitor activated" : "Competitor deactivated",
      description: `Competitor has been ${active ? 'activated' : 'deactivated'} successfully.`,
    });
  };

  const renderClientsView = () => (
    <Card>
      <CardHeader>
        <CardTitle>Clients</CardTitle>
        <CardDescription>
          Select a client to view and manage their competitors.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Client Name</TableHead>
              <TableHead>Domain</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {clients.map((client) => (
              <TableRow key={client.id}>
                <TableCell className="font-medium">{client.name}</TableCell>
                <TableCell>{client.domain}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{client.slug}</Badge>
                </TableCell>
                <TableCell>
                  <Button 
                    size="sm" 
                    onClick={() => handleClientSelect(client)}
                  >
                    View Competitors
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );

  const renderCompetitorsView = () => (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {selectedClient && isAdminOrEditor && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedClient(null);
                    setCompetitors([]);
                  }}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              Competitors
              {selectedClient && (
                <Badge variant="outline" className="ml-2">
                  {selectedClient.name}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {isViewer 
                ? "Manage competitor status for your project."
                : "Manage competitors for the selected client."
              }
            </CardDescription>
          </div>
          {isAdminOrEditor && selectedClient && (
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Competitor
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Competitor</DialogTitle>
                  <DialogDescription>
                    Add a new competitor domain for {selectedClient.name}.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="domain">Competitor Domain</Label>
                    <Input
                      id="domain"
                      value={newCompetitorDomain}
                      onChange={(e) => setNewCompetitorDomain(e.target.value)}
                      placeholder="competitor-domain.com"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleAddCompetitor}
                      disabled={addingCompetitor || !newCompetitorDomain.trim()}
                    >
                      {addingCompetitor && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Add Competitor
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setIsAddDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Domain</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Added</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {competitors.map((competitor) => (
              <TableRow key={competitor.id}>
                <TableCell className="font-medium">{competitor.domain}</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={competitor.active !== false}
                      onCheckedChange={(checked) => handleToggleCompetitor(competitor.id, checked)}
                      disabled={!isViewer && !isAdminOrEditor}
                    />
                    <span className="text-sm">
                      {competitor.active !== false ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  {new Date(competitor.created_at).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  {isAdminOrEditor && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDeleteCompetitor(competitor.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full">
          <AppSidebar />
          <div className="flex-1">
            <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
            <Breadcrumbs />
            <main className="flex-1 p-6">
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <div className="flex-1">
          <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          <Breadcrumbs />
          
          <main className="flex-1 p-6">
            <div className="space-y-6">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Competitors</h2>
                <p className="text-muted-foreground">
                  {isViewer 
                    ? "Manage competitor status for your project."
                    : "Manage competitors for your clients."
                  }
                </p>
              </div>

              {isViewer || selectedClient ? renderCompetitorsView() : renderClientsView()}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Competitors;