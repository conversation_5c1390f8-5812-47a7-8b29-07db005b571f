import { useState, useCallback, useEffect } from 'react';
import { format } from 'date-fns';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle, X, Brain, MessageSquare, Target } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { ApifyIntegration } from './ApifyIntegration';

interface ReportUploadProps {
  onUploadComplete?: () => void;
}

interface PendingFile {
  file: File;
  data: any;
  selectedType?: string;
  selectedClientId?: string;
}

interface UploadProgress {
  file: File;
  status: 'uploading' | 'success' | 'error';
  message?: string;
}

const reportTypes = [
  {
    key: 'google-ai-overview',
    value: 'google_ai_overview',
    title: 'Google AI Overview',
    icon: Brain,
    description: 'AI-powered insights from Google search results and trends'
  },
  {
    key: 'chatgpt-overview',
    value: 'chatgpt_results',
    title: 'ChatGPT Overview',
    icon: MessageSquare,
    description: 'ChatGPT analysis and conversation insights'
  },
  {
    key: 'brand-ai-overview',
    value: 'brand_ai_overview',
    title: 'Brand AI Overview',
    icon: Target,
    description: 'AI-driven brand analysis and competitive intelligence'
  }
];

export function ReportUpload({ onUploadComplete }: ReportUploadProps) {
  const [pendingFiles, setPendingFiles] = useState<PendingFile[]>([]);
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [step, setStep] = useState<'upload' | 'select-type' | 'processing'>('upload');
  const [clients, setClients] = useState<{id: string, name: string}[]>([]);
  const { toast } = useToast();
  const { profile } = useAuth();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const validFiles = acceptedFiles.filter(file => 
      file.type === 'application/json' || file.type === 'text/html' || file.name.endsWith('.html')
    );
    
    if (validFiles.length !== acceptedFiles.length) {
      toast({
        title: "Invalid file type",
        description: "Please upload only JSON or HTML files",
        variant: "destructive",
      });
      return;
    }

    // Parse files and move to selection step
    const pending: PendingFile[] = [];
    
    for (const file of validFiles) {
      try {
        const text = await file.text();
        let data = null;
        
        if (file.type === 'application/json') {
          data = JSON.parse(text);
          
          // Validate JSON structure
          if (!data || typeof data !== 'object') {
            throw new Error('Invalid JSON structure');
          }
        } else {
          // For HTML files, store the HTML content as data
          data = { html_content: text };
        }

        pending.push({
          file,
          data,
          selectedType: determineReportType(file.name, data) // Auto-suggest type
        });

      } catch (error) {
        toast({
          title: `Invalid ${file.type === 'application/json' ? 'JSON' : 'HTML'} file`,
          description: `Error parsing ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: "destructive",
        });
        return;
      }
    }

    setPendingFiles(pending);
    setStep('select-type');
  }, [toast]);

  const handleTypeSelection = (fileIndex: number, reportType: string) => {
    setPendingFiles(prev => prev.map((file, index) => 
      index === fileIndex ? { ...file, selectedType: reportType } : file
    ));
  };

  const handleClientSelection = (fileIndex: number, clientId: string) => {
    setPendingFiles(prev => prev.map((file, index) => 
      index === fileIndex ? { ...file, selectedClientId: clientId } : file
    ));
  };

  // Load clients when component mounts
  useEffect(() => {
    loadClients();
  }, []);

  const loadClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setClients(data || []);
    } catch (error) {
      console.error('Error loading clients:', error);
      toast({
        title: "Error loading clients",
        description: "Could not load client list",
        variant: "destructive",
      });
    }
  };

  const processUploads = async () => {
    setStep('processing');
    setIsUploading(true);
    
    const initialUploads = pendingFiles.map(({ file }) => ({
      file,
      status: 'uploading' as const,
    }));
    setUploads(initialUploads);

    for (let i = 0; i < pendingFiles.length; i++) {
      const { file, data, selectedType, selectedClientId } = pendingFiles[i];
      
      try {
        if (!selectedClientId) {
          throw new Error('No client selected');
        }

        if (!selectedType) {
          throw new Error('No report type selected');
        }

        // Check if it's an HTML file and insert into html_templates table
        if (data.html_content) {
          const { error } = await supabase
            .from('html_templates')
            .insert({
              client_id: selectedClientId,
              created_by: profile?.id,
              name: file.name.replace('.html', ''),
              description: `Uploaded HTML template for ${selectedType}`,
              html_content: data.html_content,
              is_active: true,
            });
          
          if (error) throw error;
        } else {
          // Insert JSON data into report_data table
          const { error } = await supabase
            .from('report_data')
            .insert({
              client_id: selectedClientId,
              uploaded_by: profile?.id,
              report_type: selectedType,
              data: data,
              file_name: file.name,
              date_from: extractDateFrom(data) || format(new Date(), 'yyyy-MM-dd'),
              date_to: extractDateTo(data) || format(new Date(), 'yyyy-MM-dd'),
              country: extractCountry(data),
              traffic_type: extractTrafficType(data),
            });
          
          if (error) throw error;
        }

        setUploads(prev => prev.map((upload, index) => 
          index === i 
            ? { ...upload, status: 'success', message: 'Upload successful' }
            : upload
        ));

      } catch (error) {
        console.error('Upload error:', error);
        setUploads(prev => prev.map((upload, index) => 
          index === i 
            ? { 
                ...upload, 
                status: 'error', 
                message: error instanceof Error ? error.message : 'Upload failed' 
              }
            : upload
        ));
      }
    }

    setIsUploading(false);
    toast({
      title: "Upload complete",
      description: `Processed ${pendingFiles.length} files`,
    });
    
    // Reset state
    setTimeout(() => {
      setPendingFiles([]);
      setUploads([]);
      setStep('upload');
      onUploadComplete?.();
    }, 2000);
  };

  const resetUpload = () => {
    setPendingFiles([]);
    setUploads([]);
    setStep('upload');
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/json': ['.json'],
      'text/html': ['.html'],
    },
    multiple: true,
  });

  const removeUpload = (index: number) => {
    setUploads(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      <ApifyIntegration />
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Report Data
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
        {step === 'upload' && (
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-primary/50'
              }
            `}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            {isDragActive ? (
              <p className="text-lg font-medium">Drop JSON or HTML files here...</p>
            ) : (
              <div>
                <p className="text-lg font-medium mb-2">
                  Drag & drop JSON or HTML files here, or click to select
                </p>
                <p className="text-sm text-muted-foreground">
                  Supports Google AI Overview, ChatGPT, and Brand AI reports
                </p>
              </div>
            )}
          </div>
        )}

        {step === 'select-type' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Select Report Type for Each File</h4>
              <Button variant="outline" size="sm" onClick={resetUpload}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
            
            <div className="space-y-4">
              {pendingFiles.map((pendingFile, fileIndex) => (
                <div key={fileIndex} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium text-sm">{pendingFile.file.name}</span>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Select Client</label>
                      <select
                        value={pendingFile.selectedClientId || ''}
                        onChange={(e) => handleClientSelection(fileIndex, e.target.value)}
                        className="w-full p-2 border rounded-lg bg-background"
                      >
                        <option value="">Choose client...</option>
                        {clients.map((client) => (
                          <option key={client.id} value={client.id}>
                            {client.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium mb-2 block">Select Report Type</label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        {reportTypes.map((type) => {
                          const Icon = type.icon;
                          const isSelected = pendingFile.selectedType === type.value;
                          
                          return (
                            <button
                              key={type.key}
                              onClick={() => handleTypeSelection(fileIndex, type.value)}
                              className={`
                                p-3 rounded-lg border text-left transition-all
                                ${isSelected 
                                  ? 'border-primary bg-primary/5 shadow-sm' 
                                  : 'border-muted hover:border-primary/50 hover:bg-muted/50'
                                }
                              `}
                            >
                              <div className="flex items-center gap-2 mb-2">
                                <Icon className="h-4 w-4" />
                                <span className="font-medium text-sm">{type.title}</span>
                              </div>
                              <p className="text-xs text-muted-foreground">{type.description}</p>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={resetUpload}>
                Cancel
              </Button>
              <Button 
                onClick={processUploads}
                disabled={pendingFiles.some(f => !f.selectedType || !f.selectedClientId)}
              >
                Upload Reports
              </Button>
            </div>
          </div>
        )}

        {step === 'processing' && uploads.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Upload Progress</h4>
            {uploads.map((upload, index) => (
              <div
                key={index}
                className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg"
              >
                <FileText className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{upload.file.name}</p>
                  {upload.message && (
                    <p className="text-xs text-muted-foreground">{upload.message}</p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {upload.status === 'uploading' && (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                  )}
                  {upload.status === 'success' && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  {upload.status === 'error' && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        </CardContent>
      </Card>
    </div>
  );
}

// Helper functions to extract data from JSON
function determineReportType(filename: string, data: any): string {
  const lower = filename.toLowerCase();
  if (lower.includes('google') || lower.includes('ai-overview')) {
    return 'google_ai_overview';
  }
  if (lower.includes('chatgpt') || lower.includes('gpt')) {
    return 'chatgpt_results';
  }
  if (lower.includes('brand')) {
    return 'brand_ai_overview';
  }
  
  // Fallback to analyzing data structure
  if (data.ai_overview || data.google_ai) {
    return 'google_ai_overview';
  }
  if (data.chatgpt || data.conversations) {
    return 'chatgpt_results';
  }
  
  return 'brand_ai_overview'; // default
}

function extractDateFrom(data: any): string | null {
  return data.date_from || data.dateFrom || data.start_date || null;
}

function extractDateTo(data: any): string | null {
  return data.date_to || data.dateTo || data.end_date || null;
}

function extractCountry(data: any): string | null {
  return data.country || data.location || data.region || null;
}

function extractTrafficType(data: any): string | null {
  return data.traffic_type || data.trafficType || data.source || null;
}

// Helper function to get first available client for admin users
async function getFirstClientId(): Promise<string | null> {
  const { data } = await supabase
    .from('clients')
    .select('id')
    .limit(1)
    .single();
  
  return data?.id || null;
}