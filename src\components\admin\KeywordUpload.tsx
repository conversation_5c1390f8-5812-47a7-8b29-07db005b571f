import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { Loader2, Upload, Download, FileText } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface Client {
  id: string;
  name: string;
}

interface KeywordData {
  keyword: string;
  volume: number;
  difficulty?: number;
  cpc?: number;
}

export const KeywordUpload = () => {
  const [selectedClient, setSelectedClient] = useState<string>("");
  const [uploading, setUploading] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const { toast } = useToast();
  const { profile } = useAuth();

  // Fetch clients for the dropdown
  const { data: clients, isLoading: clientsLoading } = useQuery({
    queryKey: ["clients"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("clients")
        .select("id, name")
        .order("name");
      
      if (error) throw error;
      return data as Client[];
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === "text/csv") {
      setFile(selectedFile);
    } else {
      toast({
        title: "Invalid file type",
        description: "Please select a CSV file.",
        variant: "destructive",
      });
    }
  };

  const parseCSV = (csvText: string): KeywordData[] => {
    const lines = csvText.split('\n');
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    
    // Find column indices
    const keywordIndex = headers.findIndex(h => h.includes('keyword'));
    const volumeIndex = headers.findIndex(h => h.includes('volume') || h.includes('search'));
    const difficultyIndex = headers.findIndex(h => h.includes('difficulty') || h.includes('kd'));
    const cpcIndex = headers.findIndex(h => h.includes('cpc') || h.includes('cost'));

    if (keywordIndex === -1 || volumeIndex === -1) {
      throw new Error("CSV must contain 'keyword' and 'volume' columns");
    }

    const keywords: KeywordData[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
      
      if (values.length > keywordIndex && values.length > volumeIndex) {
        const keyword = values[keywordIndex];
        const volume = parseInt(values[volumeIndex]) || 0;
        const difficulty = difficultyIndex !== -1 ? parseFloat(values[difficultyIndex]) : undefined;
        const cpc = cpcIndex !== -1 ? parseFloat(values[cpcIndex]) : undefined;
        
        if (keyword) {
          keywords.push({ keyword, volume, difficulty, cpc });
        }
      }
    }
    
    return keywords;
  };

  const handleUpload = async () => {
    if (!file || !selectedClient || !profile) {
      toast({
        title: "Missing information",
        description: "Please select a client and CSV file.",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);
    
    try {
      const csvText = await file.text();
      const keywords = parseCSV(csvText);
      
      if (keywords.length === 0) {
        throw new Error("No valid keywords found in CSV");
      }

      // Prepare data for insertion
      const keywordData = keywords.map(kw => ({
        keyword: kw.keyword,
        volume: kw.volume,
        difficulty: kw.difficulty,
        cpc: kw.cpc,
        client_id: selectedClient,
        uploaded_by: profile.id,
      }));

      // Insert keywords in batches
      const batchSize = 100;
      for (let i = 0; i < keywordData.length; i += batchSize) {
        const batch = keywordData.slice(i, i + batchSize);
        const { error } = await supabase
          .from("keywords")
          .insert(batch);
        
        if (error) throw error;
      }

      toast({
        title: "Success!",
        description: `Uploaded ${keywords.length} keywords successfully.`,
      });

      // Reset form
      setFile(null);
      setSelectedClient("");
      const fileInput = document.getElementById('csv-file') as HTMLInputElement;
      if (fileInput) fileInput.value = '';

    } catch (error: any) {
      console.error("Upload error:", error);
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload keywords.",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent = "keyword,volume,difficulty,cpc\nexample keyword,1000,0.5,1.25\nanother keyword,500,0.3,0.80";
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'keyword_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Check if user is admin
  if (profile?.role !== 'admin') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Keyword Upload
          </CardTitle>
          <CardDescription>
            Only administrators can upload keyword data.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Keywords
        </CardTitle>
        <CardDescription>
          Upload keyword data with volumes via CSV file. The CSV should contain columns for keyword, volume, difficulty (optional), and CPC (optional).
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="client-select">Select Client</Label>
          <Select value={selectedClient} onValueChange={setSelectedClient}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a client..." />
            </SelectTrigger>
            <SelectContent>
              {clientsLoading ? (
                <SelectItem value="loading" disabled>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading clients...
                </SelectItem>
              ) : (
                clients?.map((client) => (
                  <SelectItem key={client.id} value={client.id}>
                    {client.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="csv-file">CSV File</Label>
          <input
            id="csv-file"
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
          {file && (
            <p className="text-sm text-muted-foreground">
              Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
            </p>
          )}
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleUpload}
            disabled={!file || !selectedClient || uploading}
            className="flex-1"
          >
            {uploading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload Keywords
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={downloadTemplate}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Template
          </Button>
        </div>

        <div className="bg-muted p-4 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <FileText className="h-4 w-4" />
            CSV Format Requirements:
          </h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Required columns: <code>keyword</code>, <code>volume</code></li>
            <li>• Optional columns: <code>difficulty</code>, <code>cpc</code></li>
            <li>• First row should contain column headers</li>
            <li>• Use comma-separated values</li>
            <li>• Maximum file size: 10MB</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
