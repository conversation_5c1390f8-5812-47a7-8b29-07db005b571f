import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const apifyToken = Deno.env.get('APIFY_API_TOKEN');
    if (!apifyToken) {
      throw new Error('APIFY_API_TOKEN not configured');
    }

    const { taskId, clientId } = await req.json();
    
    if (!taskId || !clientId) {
      throw new Error('taskId and clientId are required');
    }

    // Fetch data from Apify (last run results)
    const apifyUrl = `https://api.apify.com/v2/actor-tasks/${taskId}/runs/last/dataset/items?token=${apifyToken}`;
    
    console.log('Fetching from Apify:', apifyUrl);
    
    const response = await fetch(apifyUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Apify API error: ${response.status} ${response.statusText}`);
    }

    const apifyData = await response.json();
    
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get the authorization header to identify the user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authorization header required');
    }

    // Get user profile
    const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
    if (!user) {
      throw new Error('Invalid user');
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (!profile) {
      throw new Error('User profile not found');
    }

    // Determine report type based on task ID
    let reportType = 'google_ai_overview';
    if (taskId.includes('chatgpt')) {
      reportType = 'chatgpt_results';
    } else if (taskId.includes('brand')) {
      reportType = 'brand_ai_overview';
    }

    // Store the data in report_data table
    const { data: reportData, error: insertError } = await supabase
      .from('report_data')
      .insert({
        client_id: clientId,
        data: apifyData,
        report_type: reportType,
        uploaded_by: profile.id,
        file_name: `apify_${taskId}_${new Date().toISOString().split('T')[0]}.json`,
        date_from: new Date().toISOString().split('T')[0],
        date_to: new Date().toISOString().split('T')[0],
      })
      .select()
      .single();

    if (insertError) {
      throw new Error(`Database error: ${insertError.message}`);
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Apify data fetched and stored successfully',
        reportId: reportData.id,
        itemCount: Array.isArray(apifyData) ? apifyData.length : 1
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Error in fetch-apify-data function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});