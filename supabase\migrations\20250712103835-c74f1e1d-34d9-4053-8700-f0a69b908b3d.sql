-- Create html_templates table for admin-uploaded HTML report templates
CREATE TABLE public.html_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  html_content TEXT NOT NULL,
  client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID NOT NULL REFERENCES public.profiles(id)
);

-- Enable Row Level Security
ALTER TABLE public.html_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for admin-only access
CREATE POLICY "Ad<PERSON> can manage all html templates" 
ON public.html_templates 
FOR ALL 
USING (get_user_role() = 'admin'::user_role);

-- Create policies for users to view their client's templates
CREATE POLICY "Users can view their client html templates" 
ON public.html_templates 
FOR SELECT 
USING (client_id = get_user_client_id());

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_html_templates_updated_at
BEFORE UPDATE ON public.html_templates
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create index for efficient template retrieval
CREATE INDEX idx_html_templates_client ON public.html_templates(client_id);
CREATE INDEX idx_html_templates_active ON public.html_templates(is_active) WHERE is_active = true;