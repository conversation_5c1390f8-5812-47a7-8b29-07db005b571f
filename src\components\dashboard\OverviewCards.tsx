import { Users, Building, FileText, Target, TrendingUp, TrendingDown } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export function OverviewCards() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const [
        { count: usersCount },
        { count: clientsCount },
        { count: reportsCount },
        { count: competitorsCount }
      ] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }),
        supabase.from('clients').select('*', { count: 'exact', head: true }),
        supabase.from('reports').select('*', { count: 'exact', head: true }),
        supabase.from('competitors').select('*', { count: 'exact', head: true })
      ]);

      return {
        users: usersCount || 0,
        clients: clientsCount || 0,
        reports: reportsCount || 0,
        competitors: competitorsCount || 0,
      };
    },
  });

  const cards = [
    {
      title: 'Total Users',
      value: stats?.users || 0,
      change: '+12%',
      trend: 'up' as const,
      icon: Users,
    },
    {
      title: 'Active Clients',
      value: stats?.clients || 0,
      change: '+8%',
      trend: 'up' as const,
      icon: Building,
    },
    {
      title: 'Reports Generated',
      value: stats?.reports || 0,
      change: '+23%',
      trend: 'up' as const,
      icon: FileText,
    },
    {
      title: 'Competitors Tracked',
      value: stats?.competitors || 0,
      change: '-2%',
      trend: 'down' as const,
      icon: Target,
    },
  ];

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Loading...</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">-</div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card) => {
        const Icon = card.icon;
        const TrendIcon = card.trend === 'up' ? TrendingUp : TrendingDown;
        
        return (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value.toLocaleString()}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendIcon 
                  className={`mr-1 h-3 w-3 ${
                    card.trend === 'up' ? 'text-emerald-600 dark:text-emerald-400' : 'text-red-600 dark:text-red-400'
                  }`} 
                />
                <span className={card.trend === 'up' ? 'text-emerald-600 dark:text-emerald-400' : 'text-red-600 dark:text-red-400'}>
                  {card.change}
                </span>
                <span className="ml-1 text-foreground/70">from last month</span>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}