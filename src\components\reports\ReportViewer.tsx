import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Trophy, TrendingUp, Target, AlertTriangle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

interface ReportData {
  id: string;
  report_type: string;
  data: any;
  date_from?: string;
  date_to?: string;
  country?: string;
  traffic_type?: string;
  client_id?: string;
}

interface ReportViewerProps {
  reportData: ReportData;
}

const ReportViewer: React.FC<ReportViewerProps> = ({ reportData }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [htmlTemplate, setHtmlTemplate] = useState<string | null>(null);
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(true);
  const { profile } = useAuth();

  // Get current client ID
  const getCurrentClientId = () => {
    return reportData.client_id || profile?.client_id;
  };

  // Load HTML template for current client
  useEffect(() => {
    const loadHtmlTemplate = async () => {
      try {
        const clientId = getCurrentClientId();
        
        if (!clientId) {
          setIsLoadingTemplate(false);
          return;
        }

        // Get the most recent HTML template for this client
        const { data, error } = await supabase
          .from('html_templates')
          .select('html_content, name')
          .eq('client_id', clientId)
          .eq('is_active', true)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        console.log('Loading HTML template for client:', clientId);

        if (error) {
          console.error('Error loading HTML template:', error);
        } else if (data) {
          console.log('Found HTML template, injecting report data:', reportData);
          
          // Inject the JSON data into the HTML template
          let htmlContent = data.html_content;
          
          // Replace placeholder with actual JSON data
          const jsonDataStr = JSON.stringify(reportData.data, null, 2);
          htmlContent = htmlContent.replace('{{REPORT_DATA}}', jsonDataStr);
          htmlContent = htmlContent.replace('{{JSON_DATA}}', jsonDataStr);
          
          // Inject script to make data available globally
          const scriptTag = `
            <script>
              window.reportData = ${jsonDataStr};
              window.jsonData = ${jsonDataStr};
              console.log('Injected report data:', window.reportData);
            </script>
          `;
          htmlContent = htmlContent.replace('</head>', `${scriptTag}</head>`);
          
          setHtmlTemplate(htmlContent);
        } else {
          console.log('No HTML template found for client:', clientId);
        }
      } catch (error) {
        console.error('Error loading HTML template:', error);
      } finally {
        setIsLoadingTemplate(false);
      }
    };

    loadHtmlTemplate();
  }, [reportData, profile?.client_id]);

  // If we have an HTML template, render it instead of the default view
  if (isLoadingTemplate) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <h3 className="text-lg font-medium mb-2 text-foreground">Loading Report Template...</h3>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (htmlTemplate) {
    return (
      <Card>
        <CardContent className="p-0">
          <div 
            dangerouslySetInnerHTML={{ __html: htmlTemplate }}
            className="w-full min-h-[600px]"
          />
        </CardContent>
      </Card>
    );
  }

  // Sample data structure - in real implementation this would come from reportData.data
  const sampleData = {
    overview: {
      title: "מקומות ראשונים - 25/06/2025",
      subtitle: "הישג חדש היום! 🚀",
      achievement: "🏆 הלוואה לרכב - מקום #1",
      achievementDesc: "שיפור משמעותי בקטגוריית הרכב",
      stats: [
        { label: "כרטיס אשראי טיסות", status: "✓" },
        { label: "כרטיס אשראי קריפטו", status: "✓" },
        { label: "כרטיס נקודות טיסה", status: "✓" },
        { label: "סליקה אשראי", status: "✓" }
      ],
      summary: [
        { value: "30%", label: "Live AI" },
        { value: "70%", label: "Static AI" },
        { value: "593", label: "הופעות MAX" },
        { value: "61", label: "מספר מילות מפתח" }
      ]
    },
    detailed: {
      insights: [
        {
          icon: <TrendingUp className="h-6 w-6" />,
          title: "צמצום הפער התחרותי",
          description: "המשך עדכון תכני מבני הלוואות קיימים. הפער מתחרינו מצטמצם ונכון ליום זה קריטיות המשימה גבוהה"
        },
        {
          icon: <Target className="h-6 w-6" />,
          title: "יציבות בכרטיסי אשראי",
          description: "יציבת דפי נוחיה ממוקדים לשאילתות כרטיסי אשראי עם השואות מקיפות ומתשובוים"
        },
        {
          icon: <AlertTriangle className="h-6 w-6" />,
          title: "אתגרים בביטוח",
          description: "צמצום משמעותי חשיפתנו זו כבר 16-8 חודשים ברציפות במונחי קטגוריאל גנוזה בתא מקצועי"
        }
      ],
      keywords: [
        { keyword: "כרטיס אשראי", type: "Static", sources: 3, ranking: 2 },
        { keyword: "ויזה", type: "Static", sources: 3, ranking: 3 },
        { keyword: "מאסטרקארד", type: "Static", sources: 3, ranking: 3 },
        { keyword: "הלוואות לכל מטרה", type: "Static", sources: 3, ranking: 2 },
        { keyword: "הלוואה", type: "Static", sources: 3, ranking: 2 },
        { keyword: "הלוואה חוץ בנקאית", type: "Static", sources: 3, ranking: 3 },
        { keyword: "הלוואה לכל מטרה", type: "Static", sources: 3, ranking: 5 },
        { keyword: "הלוואה מיידית", type: "Live", sources: 3, ranking: 2 },
        { keyword: "הלוואה לרכב", type: "Static", sources: 3, ranking: 1 },
        { keyword: "מימון לרכב", type: "Static", sources: 3, ranking: 2 }
      ]
    }
  };

  const competitiveData = [
    { name: '24/06/2025', MAX: 58, CAL: 76, ישראכרט: 40 },
    { name: '25/06/2025', MAX: 61, CAL: 74, ישראכרט: 42 }
  ];

  const categoryData = [
    { name: 'הלוואות', MAX: 8, ישראכרט: 6, כאל: 7 },
    { name: 'כרטיסי אשראי', MAX: 6, ישראכרט: 7, כאל: 8 },
    { name: 'ביטוח', MAX: 3, ישראכרט: 1, כאל: 2 },
    { name: 'רכב', MAX: 4, ישראכרט: 2, כאל: 3 },
    { name: 'שירותי חו"ל', MAX: 2, ישראכרט: 1, כאל: 1 }
  ];

  const trendData = [
    { date: '16/06', MAX: 52, ממוצע: 48 },
    { date: '17/06', MAX: 54, ממוצע: 49 },
    { date: '18/06', MAX: 53, ממוצע: 48 },
    { date: '19/06', MAX: 55, ממוצע: 50 },
    { date: '20/06', MAX: 57, ממוצע: 51 },
    { date: '21/06', MAX: 56, ממוצע: 50 },
    { date: '22/06', MAX: 58, ממוצע: 52 },
    { date: '23/06', MAX: 59, ממוצע: 53 },
    { date: '24/06', MAX: 58, ממוצע: 52 },
    { date: '25/06', MAX: 61, ממוצע: 54 }
  ];

  const TabButton = ({ id, children, active }: { id: string; children: React.ReactNode; active: boolean }) => (
    <Button
      variant={active ? "default" : "outline"}
      onClick={() => setActiveTab(id)}
      className="text-sm font-medium"
    >
      {children}
    </Button>
  );

  return (
    <div className="min-h-screen bg-background font-heebo" dir="rtl">
      {/* Navigation */}
      <div className="sticky top-0 z-50 bg-card border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-2xl">📊</div>
              <h1 className="text-xl font-bold text-primary">AEO Dashboard</h1>
            </div>
            <div className="flex gap-2">
              <TabButton id="overview" active={activeTab === 'overview'}>
                סקירה כללית
              </TabButton>
              <TabButton id="detailed" active={activeTab === 'detailed'}>
                AI Overview - ניתוח מפורט
              </TabButton>
              <TabButton id="chatgpt" active={activeTab === 'chatgpt'}>
                ChatGPT Search - ניתוח
              </TabButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <>
            {/* Header */}
            <Card className="text-center">
              <CardHeader className="pb-4">
                <CardTitle className="text-3xl font-bold text-foreground mb-2">
                  {sampleData.overview.title}
                </CardTitle>
                <p className="text-lg text-muted-foreground">
                  {sampleData.overview.subtitle}
                </p>
              </CardHeader>
            </Card>

            {/* Achievement Section */}
            <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
              <CardContent className="p-8">
                <div className="text-center mb-6">
                  <div className="text-2xl font-bold text-primary mb-2">
                    {sampleData.overview.achievement}
                  </div>
                  <p className="text-muted-foreground text-lg">
                    {sampleData.overview.achievementDesc}
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {sampleData.overview.stats.map((stat, index) => (
                    <div key={index} className="bg-card/80 p-4 rounded-lg text-center border">
                      <div className="text-2xl text-primary mb-1">{stat.status}</div>
                      <div className="text-sm font-medium">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Competitive Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart className="h-5 w-5" />
                  מצב תחרותי - השוואה יומית
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={competitiveData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="MAX" fill="hsl(var(--primary))" />
                      <Bar dataKey="CAL" fill="hsl(var(--destructive))" />
                      <Bar dataKey="ישראכרט" fill="hsl(var(--secondary))" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Summary Stats */}
            <Card>
              <CardHeader>
                <CardTitle>סיכום סטטיסטי</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {sampleData.overview.summary.map((stat, index) => (
                    <div key={index} className="bg-gradient-to-br from-primary/10 to-secondary/10 p-6 rounded-lg text-center border">
                      <div className="text-3xl font-bold text-primary mb-1">{stat.value}</div>
                      <div className="text-sm text-muted-foreground">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Detailed Tab */}
        {activeTab === 'detailed' && (
          <>
            {/* Header */}
            <Card className="text-center">
              <CardHeader>
                <CardTitle className="text-3xl font-bold">AI Overview - התפתחות וניתוח</CardTitle>
                <p className="text-lg text-muted-foreground">25/06/2025 - ניתוח מפורט</p>
              </CardHeader>
            </Card>

            {/* Key Insights */}
            <Card>
              <CardHeader>
                <CardTitle>תובנות מרכזיות</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  {sampleData.detailed.insights.map((insight, index) => (
                    <div key={index} className="bg-muted/30 p-6 rounded-lg border-r-4 border-primary">
                      <div className="text-primary mb-4">{insight.icon}</div>
                      <h3 className="font-semibold text-lg mb-2">{insight.title}</h3>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {insight.description}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Keywords Table */}
            <Card>
              <CardHeader>
                <CardTitle>ניתוח מילות מפתח</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-right p-3 font-semibold">מילת מפתח</th>
                        <th className="text-right p-3 font-semibold">סוג AI</th>
                        <th className="text-right p-3 font-semibold">מספר מקורות</th>
                        <th className="text-right p-3 font-semibold">דירוג MAX</th>
                      </tr>
                    </thead>
                    <tbody>
                      {sampleData.detailed.keywords.map((row, index) => (
                        <tr key={index} className="border-b hover:bg-muted/50">
                          <td className="p-3">
                            <Badge variant="default" className="bg-primary text-primary-foreground">
                              {row.keyword}
                            </Badge>
                          </td>
                          <td className="p-3">
                            <Badge variant={row.type === 'Static' ? 'secondary' : 'outline'}>
                              {row.type}
                            </Badge>
                          </td>
                          <td className="p-3">{row.sources}</td>
                          <td className="p-3 font-semibold">{row.ranking}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Performance Charts */}
            <div className="grid lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>ביצועים לפי קטגוריה</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={categoryData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="MAX" fill="hsl(var(--primary))" />
                        <Bar dataKey="ישראכרט" fill="hsl(var(--secondary))" />
                        <Bar dataKey="כאל" fill="hsl(var(--accent))" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>מגמת הופעות לאורך זמן</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={trendData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line 
                          type="monotone" 
                          dataKey="MAX" 
                          stroke="hsl(var(--primary))" 
                          strokeWidth={3}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="ממוצע" 
                          stroke="hsl(var(--secondary))" 
                          strokeWidth={3}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}

        {/* ChatGPT Tab */}
        {activeTab === 'chatgpt' && (
          <>
            <Card className="text-center">
              <CardHeader>
                <CardTitle className="text-3xl font-bold">ניתוח ChatGPT Search</CardTitle>
                <p className="text-lg text-muted-foreground">נכון לתאריך: 24/06/2025 בשעה 13:57</p>
              </CardHeader>
            </Card>

            {/* ChatGPT Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>סטטיסטיקות כלליות</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gradient-to-br from-primary/10 to-secondary/10 p-6 rounded-lg text-center">
                    <div className="text-3xl font-bold text-primary">2,186</div>
                    <div className="text-sm text-muted-foreground">תוצאות נסרקו</div>
                  </div>
                  <div className="bg-gradient-to-br from-secondary/10 to-accent/10 p-6 rounded-lg text-center">
                    <div className="text-3xl font-bold text-secondary">42%</div>
                    <div className="text-sm text-muted-foreground">הופעות TOP 3</div>
                  </div>
                  <div className="bg-gradient-to-br from-accent/10 to-primary/10 p-6 rounded-lg text-center">
                    <div className="text-3xl font-bold text-accent">5.2</div>
                    <div className="text-sm text-muted-foreground">דירוג ממוצע</div>
                  </div>
                  <div className="bg-gradient-to-br from-primary/20 to-secondary/20 p-6 rounded-lg text-center">
                    <div className="text-3xl font-bold text-primary">56</div>
                    <div className="text-sm text-muted-foreground">הופעות כולל</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Category Performance */}
            <Card>
              <CardHeader>
                <CardTitle>ביצועים לפי קטגוריה</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="bg-muted/30 p-6 rounded-lg text-center border">
                    <div className="text-3xl font-bold text-primary mb-2">15%</div>
                    <div className="text-lg font-medium mb-3">כרטיסי אשראי</div>
                    <Badge variant="destructive" className="text-xs">↓ 3% מהחודש שעבר</Badge>
                  </div>
                  <div className="bg-muted/30 p-6 rounded-lg text-center border">
                    <div className="text-3xl font-bold text-primary mb-2">20%</div>
                    <div className="text-lg font-medium mb-3">ביטוח</div>
                    <Badge variant="destructive" className="text-xs">↓ 5% מהחודש שעבר</Badge>
                  </div>
                  <div className="bg-muted/30 p-6 rounded-lg text-center border">
                    <div className="text-3xl font-bold text-primary mb-2">65%</div>
                    <div className="text-lg font-medium mb-3">הלוואות</div>
                    <Badge variant="default" className="text-xs bg-green-500">↑ 12% מהחודש שעבר</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Key Findings */}
            <Card>
              <CardHeader>
                <CardTitle>סיכום מנהלים</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-primary/5 p-6 rounded-lg border border-primary/20">
                  <h3 className="text-xl font-bold text-primary mb-4">ממצאים עיקריים:</h3>
                  <div className="space-y-4">
                    <div className="flex gap-3 p-3 border-b">
                      <Trophy className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <strong>נוכחות חזקה:</strong> MAX מופיעה ב-56 מתוך 61 שאלות עם דירוגים גבוהים בקטגוריות מפתח
                      </div>
                    </div>
                    <div className="flex gap-3 p-3 border-b">
                      <BarChart className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <strong>שליטה בהלוואות:</strong> ביצועים טובים בקטגוריית הלוואות עם 65% משאילתות
                      </div>
                    </div>
                    <div className="flex gap-3 p-3 border-b">
                      <TrendingUp className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <strong>זמינות תמורותי:</strong> דירוג ממוצע של 5.2 הופעות המיידיות, עם הופעות רבות ב-TOP 3
                      </div>
                    </div>
                    <div className="flex gap-3 p-3">
                      <Target className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                      <div>
                        <strong>הזדמנויות צמיחה:</strong> ממוציאלים שעבור בקטגוריות ביטוח וכרטיסי אשראי
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </div>
  );
};

export default ReportViewer;