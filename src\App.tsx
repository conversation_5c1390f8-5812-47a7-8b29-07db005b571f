import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "@/hooks/useAuth";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import DashboardUsers from "./pages/DashboardUsers";
import DashboardClients from "./pages/DashboardClients";
import AddClient from "./pages/AddClient";
import AddUser from "./pages/AddUser";
import Reports from "./pages/Reports";
import Competitors from "./pages/Competitors";
import Keywords from "./pages/Keywords";
import TemplateEditor from "./pages/TemplateEditor";
import HtmlTemplateUpload from "./pages/HtmlTemplateUpload";
import ResetPassword from "./pages/ResetPassword";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }
  
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
}

function PublicRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }
  
  if (user) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={
              <PublicRoute>
                <Index />
              </PublicRoute>
            } />
            <Route path="/login" element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            } />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/users" element={
              <ProtectedRoute>
                <DashboardUsers />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/clients" element={
              <ProtectedRoute>
                <DashboardClients />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/clients/add" element={
              <ProtectedRoute>
                <AddClient />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/users/add" element={
              <ProtectedRoute>
                <AddUser />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/reports" element={
              <ProtectedRoute>
                <Reports />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/reports/:reportType" element={
              <ProtectedRoute>
                <Reports />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/competitors" element={
              <ProtectedRoute>
                <Competitors />
              </ProtectedRoute>
            } />
            <Route path="/dashboard/keywords" element={
              <ProtectedRoute>
                <Keywords />
              </ProtectedRoute>
            } />
            <Route path="/admin/template-editor" element={
              <ProtectedRoute>
                <TemplateEditor />
              </ProtectedRoute>
            } />
            <Route path="/admin/html-template-upload" element={
              <ProtectedRoute>
                <HtmlTemplateUpload />
              </ProtectedRoute>
            } />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
