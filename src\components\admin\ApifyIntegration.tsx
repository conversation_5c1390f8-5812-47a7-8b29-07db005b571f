import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { Loader2, Download, ExternalLink } from "lucide-react";

interface Client {
  id: string;
  name: string;
}

export const ApifyIntegration = () => {
  const [selectedClient, setSelectedClient] = useState<string>("");
  const [taskId, setTaskId] = useState<string>("first_bench~google-ai-overview-gold");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Fetch clients
  const { data: clients = [] } = useQuery({
    queryKey: ['clients'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('clients')
        .select('id, name')
        .order('name');
      
      if (error) throw error;
      return data as Client[];
    },
  });

  const handleFetchData = async () => {
    if (!selectedClient || !taskId) {
      toast({
        title: "Missing Information",
        description: "Please select a client and enter a task ID",
        variant: "destructive",
      });
      return;
    }

    // For now, show a message that this feature needs configuration
    toast({
      title: "Feature Configuration Required",
      description: "Apify integration needs to be configured with API tokens and Supabase functions. Please contact your administrator.",
      variant: "destructive",
    });

    return;

    // TODO: Uncomment and configure when Apify integration is properly set up
    /*
    setIsLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`https://vqjjmelxpudzmnejakvo.supabase.co/functions/v1/fetch-apify-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          taskId,
          clientId: selectedClient,
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to fetch Apify data');
      }

      toast({
        title: "Success!",
        description: `Fetched and stored ${result.itemCount} items from Apify`,
      });

      // Reset form
      setTaskId("first_bench~google-ai-overview-gold");
      setSelectedClient("");

    } catch (error) {
      console.error('Error fetching Apify data:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch data from Apify",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
    */
  };

  const predefinedTasks = [
    { id: "first_bench~google-ai-overview-gold", name: "Google AI Overview", type: "google_ai_overview" },
    { id: "first_bench~chatgpt-results", name: "ChatGPT Results", type: "chatgpt_results" },
    { id: "first_bench~brand-ai-overview", name: "Brand AI Overview", type: "brand_ai_overview" },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ExternalLink className="h-5 w-5" />
          Apify Integration
        </CardTitle>
        <CardDescription>
          Fetch data directly from your Apify tasks and store them as reports (Configuration Required)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="client-select">Select Client</Label>
          <Select value={selectedClient} onValueChange={setSelectedClient}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a client" />
            </SelectTrigger>
            <SelectContent>
              {clients.map((client) => (
                <SelectItem key={client.id} value={client.id}>
                  {client.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="task-select">Apify Task</Label>
          <Select value={taskId} onValueChange={setTaskId}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a predefined task or enter custom" />
            </SelectTrigger>
            <SelectContent>
              {predefinedTasks.map((task) => (
                <SelectItem key={task.id} value={task.id}>
                  {task.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="custom-task">Custom Task ID (optional)</Label>
          <Input
            id="custom-task"
            placeholder="e.g., your-username~your-task-name"
            value={taskId}
            onChange={(e) => setTaskId(e.target.value)}
          />
        </div>

        <Button 
          onClick={handleFetchData} 
          disabled={isLoading || !selectedClient || !taskId}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Fetching Data...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Fetch from Apify
            </>
          )}
        </Button>

        <div className="text-xs text-muted-foreground p-3 bg-muted rounded-md">
          <p className="font-medium mb-1 text-orange-600">⚠️ Configuration Required:</p>
          <ul className="space-y-1">
            <li>• Apify API token needs to be configured</li>
            <li>• Supabase Edge Function needs to be deployed</li>
            <li>• Contact administrator to set up this integration</li>
            <li>• Once configured, this will fetch data from Apify tasks automatically</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};