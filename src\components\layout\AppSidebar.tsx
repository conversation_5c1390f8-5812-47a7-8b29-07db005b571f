import { BarChart3, Users, Building, FileText, Target, Plus, Brain, MessageSquare, ChevronRight, Upload, Edit } from 'lucide-react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
  SidebarHeader,
  useSidebar,
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useState } from 'react';

const items = [
  { title: 'Overview', url: '/dashboard', icon: BarChart3 },
  { title: 'Users', url: '/dashboard/users', icon: Users },
  { title: 'Clients', url: '/dashboard/clients', icon: Building },
  { title: 'Competitors', url: '/dashboard/competitors', icon: Target },
];

const reportItems = [
  { title: 'Google AI Overview', url: '/dashboard/reports/google-ai-overview', icon: Brain },
  { title: 'ChatGPT Overview', url: '/dashboard/reports/chatgpt-overview', icon: MessageSquare },
  { title: 'Brand AI Overview', url: '/dashboard/reports/brand-ai-overview', icon: Target },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const { profile } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;
  const collapsed = state === 'collapsed';
  const [reportsOpen, setReportsOpen] = useState(currentPath.startsWith('/dashboard/reports'));
  
  // Check if user can access admin/editor features
  const canAccessAdminFeatures = profile?.role === 'admin' || profile?.role === 'editor';

  const isActive = (path: string) => currentPath === path;
  const getNavCls = ({ isActive }: { isActive: boolean }) =>
    isActive ? 'bg-sidebar-accent text-sidebar-accent-foreground font-medium' : 'hover:bg-sidebar-accent/50';
  
  // Filter navigation items based on user role
  const filteredItems = items.filter(item => {
    if (item.title === 'Users' || item.title === 'Clients') {
      return canAccessAdminFeatures;
    }
    return true;
  });

  return (
    <Sidebar className={collapsed ? 'w-14' : 'w-60'} collapsible="icon">
      <SidebarHeader className="p-4">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <BarChart3 className="h-4 w-4" />
            </div>
            <h2 className="text-lg font-semibold">AEO Dashboard</h2>
          </div>
        )}
        {collapsed && (
          <div className="flex justify-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
              <BarChart3 className="h-4 w-4" />
            </div>
          </div>
        )}
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel className={collapsed ? 'sr-only' : ''}>
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filteredItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink to={item.url} end className={getNavCls}>
                      <item.icon className="h-4 w-4" />
                      {!collapsed && <span>{item.title}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
              
              {/* Reports with submenu */}
              <SidebarMenuItem>
                <Collapsible open={reportsOpen} onOpenChange={setReportsOpen}>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton className={getNavCls({ isActive: currentPath.startsWith('/dashboard/reports') })}>
                      <FileText className="h-4 w-4" />
                      {!collapsed && (
                        <>
                          <span>Reports</span>
                          <ChevronRight className={`h-4 w-4 ml-auto transition-transform ${reportsOpen ? 'rotate-90' : ''}`} />
                        </>
                      )}
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  {!collapsed && (
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {reportItems.map((reportItem) => (
                          <SidebarMenuSubItem key={reportItem.title}>
                            <SidebarMenuSubButton asChild>
                              <NavLink 
                                to={reportItem.url} 
                                className={getNavCls({ isActive: currentPath === reportItem.url })}
                              >
                                <reportItem.icon className="h-4 w-4" />
                                <span>{reportItem.title}</span>
                              </NavLink>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  )}
                </Collapsible>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {!collapsed && canAccessAdminFeatures && (
          <SidebarGroup>
            <SidebarGroupLabel>Quick Actions</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={() => navigate('/dashboard/clients/add')}
                    >
                      <Plus className="h-4 w-4" />
                      <span>Create Client</span>
                    </Button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                {profile?.role === 'admin' && (
                  <>
                     <SidebarMenuItem>
                      <SidebarMenuButton asChild>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start"
                          onClick={() => navigate('/dashboard/reports')}
                        >
                          <Upload className="h-4 w-4" />
                          <span>Upload Reports & Apify</span>
                        </Button>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start"
                          onClick={() => navigate('/admin/template-editor')}
                        >
                          <Edit className="h-4 w-4" />
                          <span>Template Editor</span>
                        </Button>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                      <SidebarMenuButton asChild>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start"
                          onClick={() => navigate('/admin/html-template-upload')}
                        >
                          <FileText className="h-4 w-4" />
                          <span>Upload HTML Template</span>
                        </Button>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>
    </Sidebar>
  );
}