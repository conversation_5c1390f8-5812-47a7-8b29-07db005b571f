import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { TopBar } from '@/components/layout/TopBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import { Loader2 } from 'lucide-react';
import CryptoJS from 'crypto-js';

interface Client {
  id: string;
  name: string;
}

interface FormData {
  username: string;
  email: string;
  password: string;
  role: 'admin' | 'editor' | 'view' | '';
  selectedClients: string[];
}

const AddUser = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    password: '',
    role: '',
    selectedClients: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, name')
        .order('name');

      if (error) throw error;
      setClients(data || []);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast({
        title: "Error",
        description: "Failed to load clients",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ 
      ...prev, 
      role: value as FormData['role'],
      selectedClients: [] // Reset client selection when role changes
    }));
    if (errors.role) {
      setErrors(prev => ({ ...prev, role: '' }));
    }
  };

  const handleClientSelection = (clientId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      selectedClients: checked 
        ? [...prev.selectedClients, clientId]
        : prev.selectedClients.filter(id => id !== clientId)
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!formData.role) {
      newErrors.role = 'Role is required';
    }

    // Client selection validation
    if (formData.role === 'view') {
      if (formData.selectedClients.length !== 1) {
        newErrors.clients = 'View users must be assigned to exactly one client';
      }
    } else if (formData.role === 'editor') {
      if (formData.selectedClients.length === 0) {
        newErrors.clients = 'Editor users must be assigned to at least one client';
      }
    }
    // Admin users don't need client selection

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);

    try {
      // Hash password with MD5
      const hashedPassword = CryptoJS.MD5(formData.password).toString();

      // Create auth user first
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          emailRedirectTo: `${window.location.origin}/`,
          data: {
            first_name: formData.username,
            last_name: ''
          }
        }
      });

      if (authError) throw authError;

      if (!authData.user) {
        throw new Error('Failed to create user');
      }

      // Update the profile with role and client assignment
      const profileData: any = {
        role: formData.role,
        first_name: formData.username,
      };

      // For view users, set direct client_id
      if (formData.role === 'view') {
        profileData.client_id = formData.selectedClients[0];
      }

      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('user_id', authData.user.id);

      if (profileError) throw profileError;

      // For editor users, create entries in user_clients table
      if (formData.role === 'editor' && formData.selectedClients.length > 0) {
        // First get the profile id
        const { data: profile, error: profileFetchError } = await supabase
          .from('profiles')
          .select('id')
          .eq('user_id', authData.user.id)
          .single();

        if (profileFetchError) throw profileFetchError;

        const userClientEntries = formData.selectedClients.map(clientId => ({
          user_id: profile.id,
          client_id: clientId
        }));

        const { error: userClientsError } = await supabase
          .from('user_clients')
          .insert(userClientEntries);

        if (userClientsError) throw userClientsError;
      }

      toast({
        title: "User created",
        description: `User has been created successfully with ${formData.role} role.`,
      });

      navigate('/dashboard/users');
    } catch (error: any) {
      console.error('Error creating user:', error);
      toast({
        title: "Error",
        description: `Failed to create user: ${error?.message || 'Please try again.'}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const renderClientSelection = () => {
    if (formData.role === 'admin') {
      return (
        <div className="text-sm text-muted-foreground">
          Admin users have access to all clients automatically.
        </div>
      );
    }

    if (!formData.role) {
      return (
        <div className="text-sm text-muted-foreground">
          Please select a role first.
        </div>
      );
    }

    const maxSelection = formData.role === 'view' ? 1 : clients.length;
    const selectionText = formData.role === 'view' 
      ? 'Select exactly one client (view-only access)'
      : 'Select one or more clients (edit access)';

    return (
      <div className="space-y-3">
        <div className="text-sm text-muted-foreground">
          {selectionText}
        </div>
        <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto border rounded-md p-3">
          {clients.map((client) => {
            const isSelected = formData.selectedClients.includes(client.id);
            const isDisabled = formData.role === 'view' && formData.selectedClients.length >= 1 && !isSelected;
            
            return (
              <div key={client.id} className="flex items-center space-x-2">
                <Checkbox
                  id={client.id}
                  checked={isSelected}
                  disabled={isDisabled}
                  onCheckedChange={(checked) => handleClientSelection(client.id, !!checked)}
                />
                <Label 
                  htmlFor={client.id} 
                  className={`text-sm ${isDisabled ? 'text-muted-foreground' : ''}`}
                >
                  {client.name}
                </Label>
              </div>
            );
          })}
        </div>
        {formData.role === 'view' && (
          <div className="text-xs text-muted-foreground">
            View users can only be assigned to one client and will have read-only access.
          </div>
        )}
      </div>
    );
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <div className="flex-1">
          <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          <Breadcrumbs />
          
          <main className="flex-1 p-6">
            <div className="max-w-2xl mx-auto">
              <div className="mb-6">
                <h1 className="text-3xl font-bold">Add New User</h1>
                <p className="text-muted-foreground">
                  Create a new user account with role-based permissions.
                </p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>User Information</CardTitle>
                  <CardDescription>
                    Enter the details for the new user account.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="username">Username *</Label>
                        <Input
                          id="username"
                          type="text"
                          value={formData.username}
                          onChange={(e) => handleInputChange('username', e.target.value)}
                          placeholder="Enter username"
                          className={errors.username ? 'border-destructive' : ''}
                        />
                        {errors.username && (
                          <p className="text-sm text-destructive">{errors.username}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="Enter email address"
                          className={errors.email ? 'border-destructive' : ''}
                        />
                        {errors.email && (
                          <p className="text-sm text-destructive">{errors.email}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password">Password *</Label>
                      <Input
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        placeholder="Enter password (will be MD5 hashed)"
                        className={errors.password ? 'border-destructive' : ''}
                      />
                      {errors.password && (
                        <p className="text-sm text-destructive">{errors.password}</p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Password will be automatically hashed using MD5 for security.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="role">User Role *</Label>
                      <Select value={formData.role} onValueChange={handleRoleChange}>
                        <SelectTrigger className={errors.role ? 'border-destructive' : ''}>
                          <SelectValue placeholder="Select user role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin">Admin - Full system access</SelectItem>
                          <SelectItem value="editor">Editor - Multi-client report editing</SelectItem>
                          <SelectItem value="view">View - Single client read-only</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.role && (
                        <p className="text-sm text-destructive">{errors.role}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>Client Access</Label>
                      {renderClientSelection()}
                      {errors.clients && (
                        <p className="text-sm text-destructive">{errors.clients}</p>
                      )}
                    </div>

                    <div className="flex gap-3 pt-4">
                      <Button type="submit" disabled={loading}>
                        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Create User
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate('/dashboard/users')}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default AddUser;