import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { BarChart3, Users, Building, FileText, Target } from 'lucide-react';

const Index = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center space-y-8 max-w-2xl px-4">
        <div className="flex justify-center mb-8">
          <div className="flex h-16 w-16 items-center justify-center rounded-xl bg-primary text-primary-foreground">
            <BarChart3 className="h-8 w-8" />
          </div>
        </div>
        
        <div className="space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">AEO Dashboard</h1>
          <p className="text-xl text-muted-foreground">
            Manage your reports, users, and analytics with our clean, powerful admin interface.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 my-8">
          <div className="flex flex-col items-center p-4 rounded-lg border bg-card">
            <Users className="h-8 w-8 mb-2 text-muted-foreground" />
            <span className="text-sm font-medium">Users</span>
          </div>
          <div className="flex flex-col items-center p-4 rounded-lg border bg-card">
            <Building className="h-8 w-8 mb-2 text-muted-foreground" />
            <span className="text-sm font-medium">Clients</span>
          </div>
          <div className="flex flex-col items-center p-4 rounded-lg border bg-card">
            <FileText className="h-8 w-8 mb-2 text-muted-foreground" />
            <span className="text-sm font-medium">Reports</span>
          </div>
          <div className="flex flex-col items-center p-4 rounded-lg border bg-card">
            <Target className="h-8 w-8 mb-2 text-muted-foreground" />
            <span className="text-sm font-medium">Competitors</span>
          </div>
        </div>
        
        <div className="flex gap-4 justify-center">
          <Button asChild size="lg">
            <Link to="/login">Get Started</Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link to="/login">Sign In</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Index;
