import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { TopBar } from '@/components/layout/TopBar';
import { Breadcrumbs } from '@/components/layout/Breadcrumbs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { X, Plus, Upload, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const AddClient = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    language: 'english',
    domain: '',
    logoFile: null as File | null,
    logoUrl: '',
  });
  const [competitors, setCompetitors] = useState<string[]>(['']);
  const [keywords, setKeywords] = useState<string[]>(['']);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const navigate = useNavigate();
  const { toast } = useToast();

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleNameChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      name: value,
      slug: prev.slug || generateSlug(value)
    }));
  };

  const addCompetitor = () => {
    if (competitors.length < 10) {
      setCompetitors([...competitors, '']);
    }
  };

  const removeCompetitor = (index: number) => {
    if (competitors.length > 1) {
      setCompetitors(competitors.filter((_, i) => i !== index));
    }
  };

  const updateCompetitor = (index: number, value: string) => {
    const updated = [...competitors];
    updated[index] = value;
    setCompetitors(updated);
  };

  const addKeyword = () => {
    setKeywords([...keywords, '']);
  };

  const removeKeyword = (index: number) => {
    setKeywords(keywords.filter((_, i) => i !== index));
  };

  const updateKeyword = (index: number, value: string) => {
    const updated = [...keywords];
    updated[index] = value;
    setKeywords(updated);
  };

  const handleLogoUpload = async (file: File) => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `logos/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('reports')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('reports')
        .getPublicUrl(filePath);

      setFormData(prev => ({
        ...prev,
        logoUrl: publicUrl
      }));

      toast({
        title: "Logo uploaded",
        description: "Logo has been uploaded successfully.",
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast({
        title: "Upload failed",
        description: "Failed to upload logo. Please try again.",
        variant: "destructive",
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Client name is required';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug is required';
    }

    if (!formData.domain.trim()) {
      newErrors.domain = 'Domain is required';
    }

    const validCompetitors = competitors.filter(c => c.trim());
    if (validCompetitors.length === 0) {
      newErrors.competitors = 'At least one competitor domain is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);

    try {
      // Create client
      const { data: client, error: clientError } = await supabase
        .from('clients')
        .insert({
          name: formData.name,
          slug: formData.slug,
          language: formData.language,
          domain: formData.domain,
          logo_url: formData.logoUrl || null,
          keywords: keywords.filter(k => k.trim()),
        })
        .select()
        .single();

      if (clientError) throw clientError;

      // Create competitors
      const validCompetitors = competitors.filter(c => c.trim());
      if (validCompetitors.length > 0) {
        const { error: competitorsError } = await supabase
          .from('competitors')
          .insert(
            validCompetitors.map(domain => ({
              client_id: client.id,
              domain: domain.trim(),
            }))
          );

        if (competitorsError) throw competitorsError;
      }

      toast({
        title: "Client created",
        description: "Client has been created successfully.",
      });

      navigate('/dashboard/clients');
    } catch (error) {
      console.error('Error creating client:', error);
      console.log('Full error details:', JSON.stringify(error, null, 2));
      toast({
        title: "Error",
        description: `Failed to create client: ${error?.message || 'Please try again.'}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <div className="flex-1">
          <TopBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
          <Breadcrumbs />
          
          <main className="flex-1 p-6">
            <div className="max-w-2xl mx-auto space-y-6">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Add New Client</h2>
                <p className="text-muted-foreground">
                  Create a new client with their details and competitors.
                </p>
              </div>

              <form onSubmit={handleSubmit}>
                <Card>
                  <CardHeader>
                    <CardTitle>Client Information</CardTitle>
                    <CardDescription>
                      Enter the basic details for the new client.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Client Name *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleNameChange(e.target.value)}
                          placeholder="Enter client name"
                        />
                        {errors.name && (
                          <p className="text-sm text-destructive">{errors.name}</p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="slug">Slug *</Label>
                        <Input
                          id="slug"
                          value={formData.slug}
                          onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                          placeholder="client-slug"
                        />
                        {errors.slug && (
                          <p className="text-sm text-destructive">{errors.slug}</p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="language">Report Language</Label>
                        <Select value={formData.language} onValueChange={(value) => 
                          setFormData(prev => ({ ...prev, language: value }))
                        }>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="english">English</SelectItem>
                            <SelectItem value="hebrew">Hebrew</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="domain">Domain *</Label>
                        <Input
                          id="domain"
                          value={formData.domain}
                          onChange={(e) => setFormData(prev => ({ ...prev, domain: e.target.value }))}
                          placeholder="example.com"
                        />
                        {errors.domain && (
                          <p className="text-sm text-destructive">{errors.domain}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Logo (Optional)</Label>
                      <div className="flex items-center gap-4">
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              setFormData(prev => ({ ...prev, logoFile: file }));
                              handleLogoUpload(file);
                            }
                          }}
                        />
                        {formData.logoUrl && (
                          <div className="flex items-center gap-2">
                            <img src={formData.logoUrl} alt="Logo" className="h-8 w-8 object-cover rounded" />
                            <span className="text-sm text-green-600">Uploaded</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <Label>Competitors * (minimum 1, maximum 10)</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addCompetitor}
                          disabled={competitors.length >= 10}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add
                        </Button>
                      </div>
                      {competitors.map((competitor, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={competitor}
                            onChange={(e) => updateCompetitor(index, e.target.value)}
                            placeholder="competitor-domain.com"
                          />
                          {competitors.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              onClick={() => removeCompetitor(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      ))}
                      {errors.competitors && (
                        <p className="text-sm text-destructive">{errors.competitors}</p>
                      )}
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <Label>Top Keywords</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addKeyword}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add
                        </Button>
                      </div>
                      {keywords.map((keyword, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={keyword}
                            onChange={(e) => updateKeyword(index, e.target.value)}
                            placeholder="keyword"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={() => removeKeyword(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>

                    <div className="flex gap-4 pt-6">
                      <Button type="submit" disabled={loading}>
                        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Create Client
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate('/dashboard/clients')}
                      >
                        Cancel
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </form>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default AddClient;